plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    // START: FlutterFire Configuration
    id "com.google.gms.google-services"
    // END: FlutterFire Configuration
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion 35
    // ndkVersion definida explicitamente para evitar erro. Anteriormente era: `ndkVersion flutter.ndkVersion`
    ndkVersion = "25.1.8937393"
    namespace 'br.com.solutiontech.stportalapp.capitalmarkets'

    androidResources {
        noCompress 'tflite'
    }

    lint {
        disable 'InvalidPackage'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }


    defaultConfig {
        applicationId "br.com.solutiontech.stportalapp.capitalmarkets"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }

    dataBinding.enabled = true

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    rootProject.allprojects {
      repositories {
          maven { url "https://repo.combateafraude.com/android/release" }
          maven { url 'https://raw.githubusercontent.com/iProov/android/master/maven/' }
      }
    }


    flavorDimensions "mode"

    productFlavors {
        prod {
          dimension "mode"
          resValue "string", "app_name", "CM Capital"
        }
        hml {
          dimension "mode"
          resValue "string", "app_name", "CM Capital HML"
          applicationIdSuffix ".hml"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "com.appsflyer:af-android-sdk:6.12.2"
}
