import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../models/trade/trade_history_recommendation.dart';
import '../../utils/extensions.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/info/border_wrap.dart';
import '../../widgets/info/vertical_field.dart';
import '../../widgets/input/dropdown_search.dart';
import '../../widgets/percentage/percentage_chip.dart';
import '../../widgets/points/point_chip.dart';
import '../../widgets/table/table_builder.dart';

abstract class TradeHistoryUnhiredBody extends StatelessWidget {
  const TradeHistoryUnhiredBody({super.key});

  TradeHistoryPageController get controller;
  String get tradeLabel;
  String get tradeName;
  String hiringRouteNameBuilder(int? id);
  String get informationText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Trade
        BorderWrap(
          margin: AppTheme.horizontalPadding,
          children: [
            VerticalField(tradeLabel, tradeName, widthFactor: 0.5),
            VerticalField(
              'Acumulado${controller.trade?.firstActivityDate != null ? ' desde ${controller.trade!.firstActivityDate!.toddMMyyyy()}' : ''}',
              controller.trade?.isBmf == true ? controller.trade?.performancePercentage.toPointsString() : controller.trade?.performancePercentage.asPercentage(showPlusSign: true),
              widthFactor: 0.5,
            ),
          ],
        ),

        // Seleção de mês/ano
        Obx(
          () => DropdownSearch<DateTime>(
            padding: AppTheme.horizontalPadding.copyWith(top: 10),
            prefixIcon: const Icon(Icons.calendar_month, color: AppTheme.orangeColor),
            initialItem: controller.selectedMonth.value,
            listItemDisplay: (date) => date.toMonthAndYear(),
            onItemSelected: controller.getHistory,
            onSearch: (search) async => controller.filterMonths(search),
            nullable: false,
          ),
        ),

        // Tabela
        Obx(
          () => controller.didThrowHistoryError.isTrue
              ? DatalessStateWidget(
                  text: 'Não foi possível carregar as operações.',
                  buttonText: 'Tentar novamente',
                  onButtonPressed: () => controller.getHistory(controller.selectedMonth.value),
                )
              : TableBuilder<TradeHistoryRecommendation>(
                  isLoading: controller.isLoadingHistory.value,
                  objects: controller.tradeHistory?.recommendations ?? [],
                  columns: [
                    ColumnBuilder('Data da\nrecomendação', (history) => history.startDate?.toddMMyyyy() ?? ''),
                    ColumnBuilder('Compra/\nVenda', (history) => history.positionType?.description ?? ''),
                    ColumnBuilder('Ticker', (history) => history.ticker ?? ''),
                    ColumnBuilder(
                        'Entrada', (history) => controller.trade?.isBmf == true ? history.entryPrice.toPointsString(showPlusSign: false) : history.entryPrice.asCurrency()),
                    ColumnBuilder('Data de\nencerramento', (history) => history.endDate?.toddMMyyyy() ?? ''),
                    ColumnBuilder('Saída', (history) => controller.trade?.isBmf == true ? history.exitPrice.toPointsString(showPlusSign: false) : history.exitPrice.asCurrency()),
                    ColumnBuilder.widget(
                      'Resultado',
                      (history) => UnconstrainedBox(
                        child: controller.trade?.isBmf == true
                            ? PointChip(
                                history.resultPercentage ?? 0,
                                preffix: (history.resultPercentage ?? 0) < 0 ? 'LOSS' : 'GAIN',
                              )
                            : PercentageChip(
                                history.resultPercentage ?? 0,
                                preffix: (history.resultPercentage ?? 0) < 0 ? 'LOSS' : 'GAIN',
                              ),
                      ),
                    ),
                  ],
                ),
        ),

        // Botão de ativação
        Button.elevated(
          text: 'Ativar',
          onPressed: () {
            Get.toNamedAndPopExistent(hiringRouteNameBuilder(controller.trade?.id), arguments: controller.trade);
          },
          margin: const EdgeInsets.fromLTRB(16, 32, 16, 42).add(AppTheme.horizontalPadding),
        ),

        // Informações
        Padding(
          padding: AppTheme.pagePadding.copyWith(top: 16),
          child: Text(informationText, style: AppTheme.regular12White),
        ),
      ],
    );
  }
}
