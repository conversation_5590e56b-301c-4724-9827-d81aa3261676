import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeMinimumBalancePage extends StatelessWidget {
  const TradeMinimumBalancePage({super.key});

  @override
  Widget build(BuildContext context) {
    final (isActive, equationText) = Get.arguments as (bool?, String?);
    return AppScaffold(
      appBar: TopBar(
        title: 'Saldo necessário para ${isActive == true ? 'editar' : 'ativar'} o serviço',
      ),
      extendBody: true,
      body: CMBody(
        child: SingleChildScrollView(
          padding: AppTheme.pagePadding,
          // child: Text(controller.equationText),
          child: Text(equationText ?? ''),
        ),
      ),
    );
  }
}
