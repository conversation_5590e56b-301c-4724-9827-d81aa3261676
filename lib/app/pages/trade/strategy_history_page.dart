import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/strategy_history_page_controller.dart';
import 'strategy_history_hired_body.dart';
import 'strategy_history_unhired_body.dart';
import 'strategy_list_page.dart';
import 'trade_history_page.dart';

class StrategyHistoryPage extends StatelessWidget {
  const StrategyHistoryPage({super.key});

  static const routeName = '${StrategyListPage.routeName}/:id/historico';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: StrategyHistoryPageController(),
      builder: (controller) {
        return TradeHistoryPage(
          controller: controller,
          bodyBuilder: (isHired) => isHired ? StrategyHistoryHiredBody(controller) : StrategyHistoryUnhiredBody(controller),
        );
      },
    );
  }
}
