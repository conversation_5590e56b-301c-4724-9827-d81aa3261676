import 'analyst_list_page.dart';
import 'trade_how_it_works_page.dart';

class AnalystTradeHowItWorksPage extends TradeHowItWorksPage {
  const AnalystTradeHowItWorksPage({super.key});

  static const routeName = '${AnalystListPage.routeName}/como-funciona';

  @override
  String get bodyText => 'Não opere sozinho: Descubra o segredo dos investidores de sucesso!'
      '\n\nCom o Trade do Analista, você não precisa de tempo nem experiência para operar no mercado e multiplicar suas chances de retorno.'
      '\n\nAo ativar o Trade do Analista, você segue automaticamente as recomendações de um analista CNPI certificado, com segurança e praticidade para suas operações.'
      '\n\nConheça as estratégias, confira os resultados e escolha o seu analista favorito agora mesmo!';

  @override
  String get bmfDecoratedText =>
      '{Atenção:} Ao ativar o Trade do Analista BMF, você não poderá alocar limites para operações manuais no mercado futuro. Caso deseje operar contratos como WIN e WDO manualmente, além das operações automatizadas pelo Trade do Analista BMF, será necessário entrar em contato com a nossa equipe de atendimento para ajustes.';
}
