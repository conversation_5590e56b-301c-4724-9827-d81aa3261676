import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../controllers/trade/trade_hiring_page_controller.dart';
import '../../models/trade/trade.dart';
import '../../utils/extensions.dart';
import '../../utils/formatters.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/buttons/info_button.dart';
import '../../widgets/card/transfer_suggestion_card.dart';
import '../../widgets/info/available_balance.dart';
import '../../widgets/info/decorated_text.dart';
import '../../widgets/info/distributor_fee_disclaimer.dart';
import '../../widgets/info/horizontal_field.dart';
import '../../widgets/info/vertical_field.dart';
import '../../widgets/input/input.dart';
import '../../widgets/input/investment_amount_input.dart';
import '../../widgets/input/labeled_checkbox.dart';
import '../../widgets/loading/skeleton.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeHiringPage extends StatelessWidget {
  final TradeHiringPageController controller;
  final String howItWorksPageRouteName;
  final void Function(Trade trade) tryNavigateToTradetHistory;
  final String descriptionPageRouteName;
  final String minimumBalancePageRouteName;

  const TradeHiringPage({
    super.key,
    required this.controller,
    required this.howItWorksPageRouteName,
    required this.tryNavigateToTradetHistory,
    required this.descriptionPageRouteName,
    required this.minimumBalancePageRouteName,
  });

  @override
  Widget build(BuildContext context) {
    final isActive = controller.trade?.isActive == true;

    return AppScaffold(
      appBar: TopBar(
        title: '${isActive ? 'Editar' : 'Ativação'} ${controller.serviceTitle}',
        icon: InfoButton(onPressed: () {
          Get.toNamed(howItWorksPageRouteName, arguments: controller.trade?.isBmf);
        }),
      ),
      extendBody: true,
      resizeToAvoidBottomInset: true,
      body: CMBody(
        onGetData: controller.loadTradeAndBalance,
        ignoreEmptyState: true,
        child: Theme(
          data: ThemeData(
            textTheme: TextTheme(
              labelSmall: AppTheme.regular12Orange.copyWith(fontWeight: FontWeight.w400),
              bodyLarge: AppTheme.bold14White,
            ),
          ),
          child: SingleChildScrollView(
            padding: AppTheme.pagePadding,
            child: Form(
              key: controller.formKey,
              autovalidateMode: controller.autovalidateMode.value,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nome do trade
                  VerticalField(
                    controller.tradeTile,
                    controller.tradeName(controller.trade),
                    widthFactor: 1,
                  ),

                  // Valor mínimo
                  HorizontalField(
                    'Valor mínimo para alocação',
                    '${controller.trade?.minValuePerOperation.asCurrency()} ${controller.trade?.isBmf == true ? '(${controller.trade?.minimumNumberOfContracts.formattedContract()})' : ''}',
                  ),

                  // Ganho
                  HorizontalField(
                    'Ganho acumulado no ano',
                    controller.trade?.isBmf == true
                        ? (controller.trade?.performancePercentage ?? 0).toPointsString()
                        : (controller.trade?.performancePercentage ?? 0).asPercentage(showPlusSign: true),
                  ),

                  // Disclaimer
                  DistributorFeeDisclaimer(
                    feeText: controller.distributorFeeText.value,
                    padding: const EdgeInsets.only(top: 10, bottom: 30),
                  ),

                  // Botões para histórico e perfil
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Button.outlined(
                          text: 'Histórico',
                          onPressed: () {
                            if (controller.trade == null) return;
                            tryNavigateToTradetHistory(controller.trade!);
                          },
                        ),
                      ),
                      const SizedBox(width: 20),
                      Flexible(
                        child: Button.outlined(
                          text: 'Perfil',
                          onPressed: () {
                            Get.toNamed(descriptionPageRouteName, arguments: controller.trade);
                          },
                        ),
                      ),
                    ],
                  ),

                  // Saldo disponível
                  const SizedBox(height: 20),
                  AvailableBalance(
                    balance: controller.balance?.availableBalance,
                    isLoading: controller.isFetchingBalance.value,
                    showSensitiveDataButton: true,
                  ),

                  // Valor investido
                  if (isActive)
                    _TextValueContainer(
                      icon: const Icon(
                        Icons.check_rounded,
                        color: AppTheme.whiteColor,
                      ),
                      text: 'Valor por recomendação atual',
                      value: controller.trade!.hiredValuePerOperation!,
                      margin: const EdgeInsets.only(bottom: 24),
                    ),

                  // Valor por recomendação (quando não é BMF)
                  if (controller.trade?.isBmf != true)
                    InvestmentAmountInput(
                      prefixIconPath: Icons.shopping_cart,
                      hintText: 'Mínimo ${controller.trade?.minValuePerOperation.asCurrency()}',
                      label: isActive ? 'Novo valor por recomendação' : 'Valor que será investido em cada recomendação',
                      inputController: controller.currencyController,
                      minimumAmount: controller.trade?.minValuePerOperation ?? 0,
                      incrementButtonsMultipliers: const [1],
                      showClearButton: true,
                      showAddSign: true,
                    )
                  // Número de contratos e valor (quando é BMF)
                  else
                    Column(
                      children: [
                        Focus(
                          onFocusChange: controller.onContractInputFucusChanged,
                          child: Input(
                            padding: const EdgeInsets.only(top: 20),
                            label: 'Número de contratos',
                            hintText: 'Mínimo ${controller.trade?.minValuePerOperation.asCurrency()} (${controller.trade?.minimumNumberOfContracts.formattedContract()})',
                            showTooltipInfo: true,
                            messageTooltipInfo: "Cada contrato corresponde a ${Constants.tradeBmfContractValue.asCurrency()}",
                            controller: controller.quantityController,
                            keyboardType: TextInputType.number,
                            inputFormatters: numberInputFormatter,
                            prefixIconPath: 'assets/icons/fields_agency.svg',
                          ),
                        ),
                        const SizedBox(height: 20),
                        Focus(
                          onFocusChange: controller.onAmountContractInputFocusChanged,
                          child: InvestmentAmountInput(
                            prefixIconPath: Icons.shopping_cart,
                            label: 'Valor que será investido',
                            inputController: controller.currencyController,
                            minimumAmount: controller.trade?.minValuePerOperation ?? 0,
                            onChangedByAmountButton: (_) => controller.updateContractQuantity(),
                            incrementButtonsMultipliers: const [1, 2, 3, 4],
                            showAddSign: true,
                          ),
                        ),
                      ],
                    ),

                  // Saldo mínimo
                  _TextValueContainer(
                    icon: Button.icon(
                      icon: Icons.info,
                      onPressed: () {
                        Get.toNamed(minimumBalancePageRouteName, arguments: (controller.trade?.isActive, controller.equationText));
                      },
                    ),
                    text: 'Saldo necessário para ${isActive ? 'editar' : 'ativar'} o serviço',
                    value: controller.minimumBalanceToInvest.value,
                    colorValidation: controller.hasMinimumBalanceToInvest,
                    isLoading: controller.isFetchingMinimumBalance.isTrue,
                    showError: controller.didThrowMinimumBalanceError.isTrue,
                    showAsterisk: isActive && controller.minimumBalanceToInvest.value <= 0,
                  ),

                  // Sugestão de transferência
                  TransferSuggestionCard(
                    margin: const EdgeInsets.only(bottom: 20),
                    minimumBalance: controller.minimumBalanceToInvest.value,
                  ),

                  // Aceite de termos
                  LabeledCheckbox(
                    customLabel: DecoratedText(
                      'Concordo com o {termo de uso do serviço}.',
                      textStyle: AppTheme.regular12White,
                      decoratedTextStyle: AppTheme.regular12Orange,
                      actions: [TapGestureRecognizer()..onTap = controller.showTerms],
                    ),
                    value: controller.hasAgreedToTerms.value,
                    onChanged: (value) {
                      controller.hasAgreedToTerms.toggle();
                      controller.update();
                    },
                  ),

                  // Ativar ou editar
                  Button.elevated(
                    text: isActive ? 'Atualizar valor' : 'Ativar',
                    onPressed: controller.invest,
                    disabled: !controller.canInvest,
                    margin: const EdgeInsets.only(top: 40),
                  ),

                  // Desativar
                  if (isActive)
                    Button.outlined(
                      text: 'Desativar',
                      textStyle: const TextStyle(color: AppTheme.errorColor),
                      borderColor: AppTheme.whiteColor,
                      leftIcon: Icons.delete_outline,
                      onPressed: controller.confirmTradeDeactivation,
                      margin: const EdgeInsets.only(top: 24),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _TextValueContainer extends StatelessWidget {
  const _TextValueContainer({
    required this.icon,
    required this.text,
    required this.value,
    this.margin = const EdgeInsets.symmetric(vertical: 24),
    this.colorValidation,
    this.isLoading = false,
    this.showError = false,
    this.showAsterisk = false,
  });

  final Widget icon;
  final String text;
  final double value;
  final bool? colorValidation;
  final EdgeInsets margin;
  final bool isLoading;
  final bool showError;
  final bool showAsterisk;

  Color get valueColor {
    if (showAsterisk) return AppTheme.greenColor3;
    if (value != 0 && colorValidation != null) {
      return colorValidation == true ? AppTheme.greenColor3 : const Color(0xFFFF5A5A);
    }
    return AppTheme.whiteColor;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 18),
      decoration: BoxDecoration(
        color: const Color(0xFF052A45),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          icon,
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                text,
                style: AppTheme.regular14White,
              ),
            ),
          ),
          if (isLoading)
            Skeleton(width: 60, height: 20, borderRadius: BorderRadius.circular(4))
          else
            Text(
              showError ? 'Erro' : '${value.asCurrency()}${showAsterisk ? '*' : ''}',
              style: showError ? AppTheme.bold14White : AppTheme.regular14White.copyWith(color: valueColor),
            ),
        ],
      ),
    );
  }
}
