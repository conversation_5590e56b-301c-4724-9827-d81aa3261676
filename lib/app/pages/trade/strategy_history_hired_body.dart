import '../../controllers/trade/strategy_history_page_controller.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../utils/extensions.dart';
import 'trade_history_hired_body.dart';
import 'strategy_hiring_page.dart';

class StrategyHistoryHiredBody extends TradeHistoryHiredBody {
  final StrategyHistoryPageController strategyController;

  const StrategyHistoryHiredBody(this.strategyController, {super.key});

  @override
  TradeHistoryPageController get controller => strategyController;

  @override
  String get tradeLabel => 'Estratégia';

  @override
  String get tradeName => controller.trade?.fullName ?? '';

  @override
  String get performanceLabel => 'Rendimento com a Estratégia desde a primeira contratação';

  @override
  String hiringRouteNameBuilder(int? id) => StrategyHiringPage.routeName.withId(id);

  @override
  String get monthResultLabel => 'Resultado com a Estratégia no mês';

  @override
  String get informationText =>
      'Operações com “*” foram encerradas antecipadamente pela Estratégia, buscando potencializar ganhos e minimizar riscos de acordo com eventos extraordinários do mercado.\n\n'
      'As ordens são executadas na abertura do mercado, no regime de melhores esforços, ou seja, por condições do próprio mercado poderá existir diferenças de valor entre o preço de entrada/saída recomendado pela Estratégia em relação ao valor executado pela CM CAPITAL.\n\n'
      'Adicionalmente, informamos que todos os preços e resultados apresentados não consideram os proventos pagos pelo ativo no decorrer da operação.';

  @override
  String get openPositionInformation =>
      'As recomendações serão registradas como encerradas apenas quando encerradas pela equipe da CM Capital conforme recomendação/estratégia. Caso o cliente cancele o serviço ou realize o encerramento das operações manualmente, as posições e preços de execução apresentadas no histórico poderão não refletir as informações reais do cliente.';
}
