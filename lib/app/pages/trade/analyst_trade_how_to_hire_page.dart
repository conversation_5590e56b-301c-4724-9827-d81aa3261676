import 'package:get/get.dart';

import '../../controllers/trade/analyst_list_page_controller.dart';
import 'analyst_list_page.dart';
import 'trade_how_to_hire_page.dart';

class AnalystTradeHowToHirePage extends TradeHowToHirePage {
  const AnalystTradeHowToHirePage({super.key});

  static const routeName = '${AnalystListPage.routeName}/como-contratar';

  @override
  String get title => 'Como contratar analistas?';

  @override
  String get bodyText =>
      'Trade do Analista é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de analistas selecionados. Clique no botão abaixo para conhecer os analistas disponíveis.';

  @override
  String get buttonText => 'Analistas Disponíveis';

  @override
  void onButtonPressed() {
    Get.find<AnalystListPageController>().tabController.animateTo(0);
    Get.toNamed(AnalystListPage.routeName);
  }
}
