import '../../models/trade/trade.dart';
import 'trade_description_page.dart';
import 'analyst_list_page.dart';

class AnalystDescriptionPage extends TradeDescriptionPage {
  const AnalystDescriptionPage({super.key});

  static const routeName = '${AnalystListPage.routeName}/descricao';

  @override
  String title(Trade? trade) => trade?.fullName ?? 'Analista não encontrado';

  @override
  String get reportsTitle => 'Relatórios do analista';
}
