import 'package:flutter/material.dart';

import '../../controllers/trade/trade_list_page_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/buttons/info_button.dart';
import '../../widgets/loading/pull_to_refresh.dart';
import '../../widgets/loading/skeleton.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/tabs/tabs.dart';
import 'trade_list_card.dart';

class TradeListPage extends StatelessWidget {
  final TradeListPageController controller;

  const TradeListPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: TopBar(
        title: controller.pageTitle,
        icon: InfoButton(onPressed: controller.showInfoPage),
      ),
      extendBody: true,
      body: CMBody(
        child: Tabs.outlined(
          controller: controller.tabController,
          tabs: [
            Tab(text: controller.availableTradesTabText),
            Tab(text: controller.hiredTradesTabText),
          ],
          children: [
            _List(
              controller: controller,
              isAvailableList: true,
            ),
            _List(
              controller: controller,
            ),
          ],
        ),
      ),
    );
  }
}

class _List extends StatelessWidget {
  final TradeListPageController controller;
  final bool isAvailableList;

  const _List({required this.controller, this.isAvailableList = false});

  int get _itemCount => isAvailableList ? controller.relevantAvailableTrades.length : controller.hiredTrades.length;

  bool get _shouldRenderShowMoreButton => isAvailableList && controller.isShowMoreButtonVisible;

  // Adiciona +1 na contagem referente ao botão
  int get _totalItems => _shouldRenderShowMoreButton ? _itemCount + 1 : _itemCount;

  @override
  Widget build(BuildContext context) {
    return PullToRefresh(
      onRefresh: controller.loadTrades,
      child: ListView.separated(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 80),
        separatorBuilder: (_, __) => const SizedBox(height: 24),
        itemCount: controller.isLoading ? 5 : _totalItems,
        itemBuilder: (_, int index) {
          if (controller.isLoading) {
            return Skeleton(height: 230, borderRadius: BorderRadius.circular(16));
          } else if (_shouldRenderShowMoreButton && index == _totalItems - 1) {
            return Button.elevated(text: "Ver Mais", onPressed: () => controller.isShowingMore = true);
          } else {
            return TradeListCard(
              trade: isAvailableList ? controller.relevantAvailableTrades[index] : controller.hiredTrades[index],
              controller: controller,
            );
          }
        },
      ),
    );
  }
}
