import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../utils/extensions.dart';
import '../../widgets/scaffold/app_scaffold.dart';

class TradeHistoryPage extends StatelessWidget {
  final TradeHistoryPageController controller;
  final Widget Function(bool isHired) bodyBuilder;

  const TradeHistoryPage({
    super.key,
    required this.controller,
    required this.bodyBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Histórico de Recomendações'),
      extendBody: true,
      resizeToAvoidBottomInset: true,
      body: CMBody(
        onGetData: controller.loadTradeAndData,
        errorText: 'Não foi possível carregar o histórico.',
        child: SingleChildScrollView(
          padding: AppTheme.pagePadding.onlyVertical,
          child: bodyBuilder(controller.trade?.isHired == true),
        ),
      ),
    );
  }
}
