import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/strategy_hiring_page_controller.dart';
import '../../controllers/trade/strategy_history_page_controller.dart';
import 'strategy_description_page.dart';
import 'strategy_list_page.dart';
import 'strategy_trade_how_it_works_page.dart';
import 'strategy_trade_minimum_balance_page.dart';
import 'trade_hiring_page.dart';

class StrategyHiringPage extends StatelessWidget {
  const StrategyHiringPage({super.key});

  static const routeName = '${StrategyListPage.routeName}/:id/ativar';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: StrategyHiringPageController(),
      builder: (controller) {
        return TradeHiringPage(
          controller: controller,
          howItWorksPageRouteName: StrategyTradeHowItWorksPage.routeName,
          tryNavigateToTradetHistory: StrategyHistoryPageController.tryNavigateToStrategyHistory,
          descriptionPageRouteName: StrategyDescriptionPage.routeName,
          minimumBalancePageRouteName: StrategyTradeMinimumBalancePage.routeName,
        );
      },
    );
  }
}
