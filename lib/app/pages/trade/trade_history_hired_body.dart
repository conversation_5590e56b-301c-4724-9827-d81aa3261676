import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_open_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../../widgets/info/border_wrap.dart';
import '../../widgets/info/vertical_field.dart';
import '../../widgets/input/dropdown_search.dart';
import '../../widgets/loading/skeleton.dart';
import '../../widgets/percentage/percentage_chip.dart';
import '../../widgets/points/point_chip.dart';
import '../../widgets/table/table_builder.dart';

abstract class TradeHistoryHiredBody extends StatelessWidget {
  const TradeHistoryHiredBody({super.key});

  TradeHistoryPageController get controller;
  String get tradeLabel;
  String get tradeName;
  String get performanceLabel;
  String hiringRouteNameBuilder(int? id);
  String get monthResultLabel;
  String get informationText;
  String get openPositionInformation;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Trade
        BorderWrap(
          margin: AppTheme.horizontalPadding,
          children: [
            VerticalField(
              tradeLabel,
              tradeName,
              widthFactor: 0.5,
            ),
            VerticalField(
              performanceLabel,
              controller.trade?.isBmf == true ? controller.trade?.performancePercentage.toPointsString() : controller.trade?.performancePercentage.asPercentage(showPlusSign: true),
              widthFactor: 1,
            ),
          ],
        ),

        // Botão de edição
        Button.elevated(
          text: 'Editar',
          onPressed: () {
            Get.toNamedAndPopExistent(hiringRouteNameBuilder(controller.trade?.id), arguments: controller.trade);
          },
          margin: const EdgeInsets.symmetric(vertical: 24).add(AppTheme.horizontalPadding),
        ),

        // Tabela de operações em aberto
        const Padding(
          padding: AppTheme.horizontalPadding,
          child: Text('Operações em aberto', style: AppTheme.bold16White),
        ),
        Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TableBuilder<TradeOpenPosition>(
                isLoading: controller.isLoadingOpenPositions.value,
                objects: controller.openPositions?.positions ?? [],
                emptyStateMessage: 'As próximas recomendações operadas aparecerão aqui!',
                columns: [
                  ColumnBuilder('Data da\noperação', (position) => position.startDate?.toddMMyyyy() ?? ''),
                  ColumnBuilder('Compra/\nVenda', (position) => position.positionType?.description ?? ''),
                  ColumnBuilder('Ticker', (position) => position.ticker ?? ''),
                  ColumnBuilder('Aplicado', (position) => controller.trade?.isBmf == true ? '${position.numberOfContracts} contratos' : position.invested.asCurrency()),
                  ColumnBuilder(
                      'Entrada', (position) => controller.trade?.isBmf == true ? position.entryPrice.toPointsString(showPlusSign: false) : position.entryPrice.asCurrency()),
                  ColumnBuilder('Gain', (position) => controller.trade?.isBmf == true ? position.gainPrice.toPointsString(showPlusSign: false) : position.gainPrice.asCurrency()),
                  ColumnBuilder('Loss', (position) => controller.trade?.isBmf == true ? position.lossPrice.toPointsString(showPlusSign: false) : position.lossPrice.asCurrency()),
                ],
              ),
              if (controller.hasOpenPosition)
                Padding(
                  padding: AppTheme.horizontalPadding.copyWith(top: 16),
                  child: Text(
                    openPositionInformation,
                    style: AppTheme.regular12White,
                  ),
                )
            ],
          ),
        ),
        // Operações fechadas
        Padding(
          padding: AppTheme.horizontalPadding.copyWith(top: 24, bottom: 8),
          child: const Text('Operações encerradas', style: AppTheme.bold16White),
        ),

        // Campo para seleção de mês
        if ((controller.trade?.monthsWithActivity ?? []).isNotEmpty)
          Obx(
            () => DropdownSearch<DateTime>(
              padding: AppTheme.horizontalPadding,
              prefixIcon: const Icon(Icons.calendar_month, color: AppTheme.orangeColor),
              initialItem: controller.selectedMonth.value,
              listItemDisplay: (date) => date.toMonthAndYear(),
              onItemSelected: controller.getClosedPositions,
              onSearch: (search) async => controller.filterMonths(search),
              nullable: false,
            ),
          ),

        // Resumo do mês
        Obx(
          () {
            return BorderWrap(
              margin: AppTheme.horizontalPadding.copyWith(top: 10),
              children: [
                VerticalField(
                  'Valor aplicado',
                  _closedPositionsLoadingOrValue(controller.closedPositions?.totalMonthInvested ?? 0),
                  widthFactor: 0.5,
                ),
                VerticalField(
                  'Resultado bruto',
                  _closedPositionsLoadingOrValue(controller.closedPositions?.totalMonthResult ?? 0, AppTheme.semi14White),
                  widthFactor: 0.5,
                ),
                VerticalField(
                  monthResultLabel,
                  _closedPositionsLoadingOrValue(controller.trade?.isBmf == true
                      ? (controller.closedPositions?.totalMonthResultPercentage ?? 0).toPointsString()
                      : (controller.closedPositions?.totalMonthResultPercentage ?? 0).asPercentage(showPlusSign: true)),
                  widthFactor: 1,
                ),
              ],
            );
          },
        ),

        // Tabela de operações fechadas
        Obx(
          () => controller.didThrowClosedPositionsError.isTrue
              ? DatalessStateWidget(
                  text: 'Não foi possível carregar as operações encerradas.',
                  buttonText: 'Tentar novamente',
                  onButtonPressed: () => controller.getClosedPositions(controller.selectedMonth.value),
                )
              : TableBuilder<TradeClosedPosition>(
                  isLoading: controller.isLoadingClosedPositions.value,
                  objects: controller.closedPositions?.positions ?? [],
                  emptyStateMessage: 'As operações encerradas aparecerão aqui!',
                  columns: [
                    ColumnBuilder('Data da\noperação', (position) => position.startDate?.toddMMyyyy() ?? ''),
                    ColumnBuilder('Compra/\nVenda', (position) => position.positionType?.description ?? ''),
                    ColumnBuilder('Ticker', (position) => position.ticker ?? ''),
                    ColumnBuilder.widget(
                      'Aplicado',
                      (position) => position.hadEnoughBalance
                          ? (controller.trade?.isBmf == true ? '${position.numberOfContracts ?? 0} contratos' : (position.invested?.asCurrency() ?? '')).asWidget()
                          : Button.icon(
                              icon: Icons.error_outline,
                              iconColor: AppTheme.orangeColor,
                              onPressed: _showInsufficientBalanceDialog,
                            ),
                    ),
                    ColumnBuilder('Data de\nencerramento', (position) => position.hadEnoughBalance ? position.endDate?.toddMMyyyy() ?? '' : ''),
                    ColumnBuilder.widget(
                      'Gain/Loss',
                      (position) => position.resultPercentage != null && position.hadEnoughBalance
                          ? controller.trade?.isBmf == true
                              ? PointChip(
                                  position.resultPercentage!,
                                  preffix: '${position.resultPercentage! < 0 ? 'LOSS' : 'GAIN'}${position.wasClosedEarly ? '*' : ''}',
                                )
                              : PercentageChip(
                                  position.resultPercentage!,
                                  preffix: '${position.resultPercentage! < 0 ? 'LOSS' : 'GAIN'}${position.wasClosedEarly ? '*' : ''}',
                                )
                          : const SizedBox.shrink(),
                    ),
                    ColumnBuilder.widget('Resultado\nbruto', (position) => position.hadEnoughBalance ? _ColoredAmount(position.resultPrice) : Container()),
                  ],
                ),
        ),

        // Informações
        if ((controller.trade?.monthsWithActivity ?? []).isNotEmpty)
          Padding(
            padding: AppTheme.pagePadding.copyWith(top: 16),
            child: Text(
              '$informationText'
              '${controller.trade?.isBmf == true ? '\n\nCada contrato corresponde a ${Constants.tradeBmfContractValue.asCurrency()}' : ''}',
              style: AppTheme.regular12White,
            ),
          ),
      ],
    );
  }

  void _showInsufficientBalanceDialog() {
    CMBottomSheet.simple(
      topWidget: Image.asset('assets/icons/dialog/investment_cancelled.png'),
      title: 'Operação não realizada',
      description: 'Saldo em conta não foi suficiente para realizar a operação no dia.',
      buttonOnPressed: Get.back,
    ).show();
  }

  /// Retorna um `Skeleton` indicador de carregamento, quando [controller.isLoadingClosedPositions.isTrue].
  ///
  /// Caso [value] seja um [double], retorna o valor formatado como moeda. Se [styleToColor] for fornecido, retorna um `Text` com o valor colorido e o sinal.
  ///
  /// Caso contrário, retorna o próprio [value].
  dynamic _closedPositionsLoadingOrValue(dynamic value, [TextStyle? styleToColor]) {
    if (controller.isLoadingClosedPositions.isTrue) {
      return const Padding(
        padding: EdgeInsets.only(top: 2),
        child: Skeleton(width: 100, height: 20),
      );
    } else if (value is num) {
      value = value.toDouble();
      if (styleToColor != null) return _ColoredAmount(value as double, styleToColor);
      return (value as double).asCurrency();
    } else {
      return value;
    }
  }
}

class _ColoredAmount extends StatelessWidget {
  final double? value;
  final TextStyle styleToColor;

  /// Exibe o valor formatado como moeda, colorindo-o de acordo com o sinal.
  const _ColoredAmount(this.value, [this.styleToColor = AppTheme.defaultText]);

  @override
  Widget build(BuildContext context) {
    if (value == null) return Container();
    final amount = '${value! < 0 ? '' : '+'}${value!.asCurrency()}';
    return Text(amount, style: styleToColor.copyWith(color: value! < 0 ? AppTheme.orangeColor : AppTheme.greenColor3));
  }
}
