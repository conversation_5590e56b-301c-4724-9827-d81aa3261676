import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/analyst_hiring_page_controller.dart';
import '../../controllers/trade/analyst_history_page_controller.dart';
import 'analyst_description_page.dart';
import 'analyst_list_page.dart';
import 'analyst_trade_how_it_works_page.dart';
import 'analyst_trade_minimum_balance_page.dart';
import 'trade_hiring_page.dart';

class AnalystHiringPage extends StatelessWidget {
  const AnalystHiringPage({super.key});

  static const routeName = '${AnalystListPage.routeName}/:id/ativar';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystHiringPageController(),
      // autoRemove: false,
      builder: (controller) {
        return TradeHiringPage(
          controller: controller,
          howItWorksPageRouteName: AnalystTradeHowItWorksPage.routeName,
          tryNavigateToTradetHistory: AnalystHistoryPageController.tryNavigateToAnalystHistory,
          descriptionPageRouteName: AnalystDescriptionPage.routeName,
          minimumBalancePageRouteName: AnalystTradeMinimumBalancePage.routeName,
        );
      },
    );
  }
}
