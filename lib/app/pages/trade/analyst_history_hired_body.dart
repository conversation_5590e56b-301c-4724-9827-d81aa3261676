import '../../controllers/trade/analyst_history_page_controller.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../utils/extensions.dart';
import 'trade_history_hired_body.dart';
import 'analyst_hiring_page.dart';

class AnalystHistoryHiredBody extends TradeHistoryHiredBody {
  final AnalystHistoryPageController analystController;

  const AnalystHistoryHiredBody(this.analystController, {super.key});

  @override
  TradeHistoryPageController get controller => analystController;

  @override
  String get tradeLabel => 'Analista';

  @override
  String get tradeName => controller.trade?.fullName ?? '';

  @override
  String get performanceLabel => 'Rendimento com o analista desde a primeira contratação';

  @override
  String hiringRouteNameBuilder(int? id) => AnalystHiringPage.routeName.withId(id);

  @override
  String get monthResultLabel => 'Resultado com o analista no mês';

  @override
  String get informationText =>
      'Operações com “*” foram encerradas antecipadamente pelo analista, buscando potencializar ganhos e minimizar riscos de acordo com eventos extraordinários do mercado.\n\n'
      'As ordens são executadas na abertura do mercado, no regime de melhores esforços, ou seja, por condições do próprio mercado poderá existir diferenças de valor entre o preço de entrada/saída recomendado pelo analista em relação ao valor executado pela CM CAPITAL.\n\n'
      'Adicionalmente, informamos que todos os preços e resultados apresentados não consideram os proventos pagos pelo ativo no decorrer da operação.';

  @override
  String get openPositionInformation =>
      'As recomendações serão registradas como encerradas apenas quando encerradas pela equipe da CM Capital conforme recomendação/estratégia. Caso o cliente cancele o serviço ou realize o encerramento das operações manualmente, as posições e preços de execução apresentadas no histórico poderão não refletir as informações reais do cliente.';
}
