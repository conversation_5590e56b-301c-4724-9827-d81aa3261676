import '../../models/trade/trade.dart';
import 'trade_description_page.dart';
import 'strategy_list_page.dart';

class StrategyDescriptionPage extends TradeDescriptionPage {
  const StrategyDescriptionPage({super.key});

  static const routeName = '${StrategyListPage.routeName}/descricao';

  @override
  String title(Trade? trade) => trade?.firstName ?? 'Estratégia não encontrada';

  @override
  String get reportsTitle => 'Relatórios da estratégia';
}
