import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/analyst_list_page_controller.dart';
import 'analyst_list_page.dart';
import 'analyst_trade_how_to_hire_page.dart';
import 'trade_my_list_page.dart';

class AnalystMyListPage extends StatelessWidget {
  const AnalystMyListPage({super.key});

  static const routeName = '/meus-investimentos/trade-do-analista';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystListPageController(),
      builder: (controller) {
        return TradeMyListPage(
          controller: controller,
          pageTitle: 'Trade do Analista',
          infoPageRouteName: AnalystTradeHowToHirePage.routeName,
          pageSubtitle: 'Analistas Contratados',
          emptyListTitleText: 'Você ainda não tem nenhum analista contratado.',
          emptyListBodyText:
              'Trade do Analista é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de analistas selecionados. Clique no botão abaixo para conhecer os analistas disponíveis.',
          emptyListButtonText: 'Contratar Analistas',
          listPageRouteName: AnalystListPage.routeName,
        );
      },
    );
  }
}
