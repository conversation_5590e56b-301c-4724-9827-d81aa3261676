import '../../controllers/trade/analyst_history_page_controller.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../utils/extensions.dart';
import 'trade_history_unhired_body.dart';
import 'analyst_hiring_page.dart';

class AnalystHistoryUnhiredBody extends TradeHistoryUnhiredBody {
  final AnalystHistoryPageController analystController;

  const AnalystHistoryUnhiredBody(this.analystController, {super.key});

  @override
  TradeHistoryPageController get controller => analystController;

  @override
  String get tradeLabel => 'Analista';

  @override
  String get tradeName => controller.trade?.fullName ?? '';

  @override
  String hiringRouteNameBuilder(int? id) => AnalystHiringPage.routeName.withId(id);

  @override
  String get informationText =>
      'O resultado do mês do analista é somado contando todas as recomendações emitidas no mês de referência. Se não houver saldo para realizar alguma das operações do mês, seu resultado (%) poderá ser maior ou menor do que o resultado (%) do analista. As operações são executadas automaticamente em ordem cronológica.';
}
