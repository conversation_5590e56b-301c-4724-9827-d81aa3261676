import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/trade/analyst_history_page_controller.dart';
import 'analyst_history_hired_body.dart';
import 'analyst_history_unhired_body.dart';
import 'analyst_list_page.dart';
import 'trade_history_page.dart';

class AnalystHistoryPage extends StatelessWidget {
  const AnalystHistoryPage({super.key});

  static const routeName = '${AnalystListPage.routeName}/:id/historico';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: AnalystHistoryPageController(),
      builder: (controller) {
        return TradeHistoryPage(
          controller: controller,
          bodyBuilder: (isHired) => isHired ? AnalystHistoryHiredBody(controller) : AnalystHistoryUnhiredBody(controller),
        );
      },
    );
  }
}
