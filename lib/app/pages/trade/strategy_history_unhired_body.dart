import '../../controllers/trade/strategy_history_page_controller.dart';
import '../../controllers/trade/trade_history_page_controller.dart';
import '../../utils/extensions.dart';
import 'trade_history_unhired_body.dart';
import 'strategy_hiring_page.dart';

class StrategyHistoryUnhiredBody extends TradeHistoryUnhiredBody {
  final StrategyHistoryPageController strategyController;

  const StrategyHistoryUnhiredBody(this.strategyController, {super.key});

  @override
  TradeHistoryPageController get controller => strategyController;

  @override
  String get tradeLabel => 'Estratégia';

  @override
  String get tradeName => controller.trade?.firstName ?? '';

  @override
  String hiringRouteNameBuilder(int? id) => StrategyHiringPage.routeName.withId(id);

  @override
  String get informationText =>
      'O resultado do mês do analista é somado contando todas as recomendações emitidas no mês de referência. Se não houver saldo para realizar alguma das operações do mês, seu resultado (%) poderá ser maior ou menor do que o resultado (%) do analista. As operações são executadas automaticamente em ordem cronológica.';
}
