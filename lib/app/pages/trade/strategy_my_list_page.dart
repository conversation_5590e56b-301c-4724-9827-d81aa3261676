import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'strategy_trade_how_to_hire_page.dart';
import 'strategy_list_page.dart';
import '../../controllers/trade/strategy_list_page_controller.dart';
import 'trade_my_list_page.dart';

class StrategyMyListPage extends StatelessWidget {
  const StrategyMyListPage({super.key});

  static const routeName = '/meus-investimentos/trade-quantitativo';

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: StrategyListPageController(),
      builder: (controller) {
        return TradeMyListPage(
          controller: controller,
          pageTitle: 'Trade Quant',
          infoPageRouteName: StrategyTradeHowToHirePage.routeName,
          pageSubtitle: 'Estratégias Contratadas',
          emptyListTitleText: 'Você ainda não tem nenhuma estratégia contratada.',
          emptyListBodyText:
              'Trade Quant é um serviço que te ajuda a realizar operações de renda variável automaticamente, com base nas recomendações de estratégias selecionadas. Clique no botão abaixo para conhecer as estratégias disponíveis.',
          emptyListButtonText: 'Contratar Estratégias',
          listPageRouteName: StrategyListPage.routeName,
        );
      },
    );
  }
}
