import 'package:flutter/material.dart';

import '../../controllers/trade/trade_list_page_controller.dart';
import '../../models/trade/trade.dart';
import '../../widgets/card/trade_card.dart';

class TradeListCard extends StatelessWidget {
  final Trade trade;
  final TradeListPageController controller;

  const TradeListCard({required this.trade, required this.controller});

  @override
  Widget build(BuildContext context) {
    return TradeCard(
      profilePictureUrl: trade.profileImageUrl,
      title: controller.cardTitle(trade),
      subtitle: trade.shortDescription ?? '',
      resultAmount: trade.performancePercentage,
      successRate: trade.successRate,
      recommendationAverage: trade.recommendationAverage,
      operationAmount: trade.hiredValuePerOperation,
      onProfilePressed: () => controller.showProfilePage(trade),
      onActivatePressed: () {
        controller.showHiringPage(trade);
      },
      onEditPressed: () {
        controller.showHiringPage(trade);
      },
      onHistoryPressed: () => controller.showHistoryPage(trade),
      isActive: trade.isActive,
      isHired: trade.isHired,
      hasHistory: trade.hasHistory,
      isBmf: trade.isBmf,
      numberOfContracts: trade.numberOfContracts,
      deactivatedText: controller.deactivatedText,
    );
  }
}
