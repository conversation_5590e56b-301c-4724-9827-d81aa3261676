import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/transfers/portability_to_request_controller.dart';
import '../../../../widgets/list/asset_quantity_selection_list.dart';
import '../../../../widgets/scaffold/portability_request_scaffold.dart';
import '../portability_request_page.dart';

class PortabilityRequestAssetSelectionPage extends StatelessWidget {
  static const routeName = '${PortabilityRequestPage.routeName}/ativos';

  const PortabilityRequestAssetSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX(
      init: PortabilityToRequestController(),
      builder: (controller) {
        return PortabilityRequestScaffold(
          title: 'Portabilidade CM Capital',
          subtitle: 'Enviar recursos',
          topText: 'Selecione os investimentos e as quantidades para seguir com a portabilidade.',
          onGetData: controller.fetchAsstes,
          bottomButtonText: controller.exchanges.isEmpty ? null : 'Continuar',
          bottomButtonOnPressed: controller.canSubmit ? controller.onExchangeSubmitted : null,
          formKey: controller.exchangeFormKey,
          formAutovalidateMode: controller.exchangeFormKeyAutovalidateMode.value,
          columnChildren: [
            const AssetQuantitySelectionList(),
          ],
        );
      },
    );
  }
}
