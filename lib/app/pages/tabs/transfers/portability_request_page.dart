import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../widgets/card/portability_choice_card.dart';
import '../../../widgets/icons/portability_choice_icon.dart';
import '../../../widgets/scaffold/app_scaffold.dart';
import '../../../widgets/tabs/tab_bar.dart';
import '../../../widgets/tabs/tabs.dart';
import 'portability/portability_request_exchange_selection_page.dart';
import 'portability_b3_page.dart';

class PortabilityRequestPage extends StatelessWidget {
  static const routeName = '/transferencias/solicitar-portabilidade';

  const PortabilityRequestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Portabilidade (STVM)'),
      extendBody: true,
      body: CMBody(
        child: Tabs(
          tabsDecoration: CMTabBar.outlinedTabBarDecoration.copyWith(indicatorSize: TabBarIndicatorSize.tab),
          tabs: const [
            Tab(text: 'Trazer recursos'),
            Tab(text: 'Enviar recursos'),
          ],
          children: [
            // Trazer recursos
            SingleChildScrollView(
              padding: AppTheme.pagePadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PortabilityChoiceCard(
                    isRecommended: true,
                    title: 'Portabilidade Total',
                    description: 'Selecione a corretora de origem e faça a portabilidade dos seus investimentos para a CM Capital',
                    icon: const PortabilityChoiceCmCapitalIcon(),
                    onTap: () {},
                  ),
                  const SizedBox(height: 12),
                  PortabilityChoiceCard(
                    title: 'Portabilidade Parcial',
                    description: 'Selecione os investimentos que deseja transferir acessando a Área do Investidor B3',
                    icon: const PortabilityChoiceB3Icon(),
                    onTap: () {},
                  ),
                ],
              ),
            ),

            // Enviar recursos
            SingleChildScrollView(
              padding: AppTheme.pagePadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PortabilityChoiceCard(
                    title: 'Portabilidade CM Capital',
                    description: 'Selecione os recursos e realize a transferência através do App da CM Capital',
                    icon: const PortabilityChoiceCmCapitalIcon(),
                    onTap: () => Get.toNamed(PortabilityRequestExchangeSelectionPage.routeName),
                  ),
                  const SizedBox(height: 12),
                  PortabilityChoiceCard(
                    title: 'Portabilidade B3',
                    description: 'Realize a transferência através da Área do Investidor da B3',
                    icon: const PortabilityChoiceB3Icon(),
                    onTap: () => Get.toNamed(PortabilityB3Page.routeName),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
