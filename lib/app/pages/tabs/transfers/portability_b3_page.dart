import 'package:flutter/material.dart';

import '../../../config/app_theme.dart';
import '../../../utils/url_handlers.dart';
import '../../../widgets/media/youtube_player.dart';
import '../../../widgets/scaffold/portability_request_scaffold.dart';
import 'portability_request_page.dart';

class PortabilityB3Page extends StatelessWidget {
  static const routeName = '${PortabilityRequestPage.routeName}/b3';

  const PortabilityB3Page({super.key});

  @override
  Widget build(BuildContext context) {
    return CMYouTubePlayer(
      videoCode: 'm6g3zcBZsLs',
      builder: (_, player) {
        return PortabilityRequestScaffold(
          title: 'Portabilidade B3',
          subtitle: 'Enviar recursos',
          topText: 'Transferências Digitais são iniciadas na Área do Investidor da B3 no menu "Portabilidade".',
          bottomButtonText: 'ACESSAR PORTAL DA B3',
          bottomButtonIcon: Icons.open_in_new,
          bottomButtonOnPressed: () => openUrl('https://www.investidor.b3.com.br/'),
          columnChildren: [
            // Instruções
            const _StepItem(
              number: 1,
              text: 'Acesse o Portal da B3 clicando no botão abaixo.',
            ),
            const _StepItem(
              number: 2,
              text: 'Selecione a instituição (origem) que deseja retirar seus investimentos.',
            ),
            const _StepItem(
              number: 3,
              text: 'Selecione a instituição (destino) que deseja enviar seus investimentos.',
            ),
            const _StepItem(
              number: 4,
              text: 'Selecione os ativos e as quantidades que deseja transferir.',
            ),
            const _StepItem(
              number: 5,
              text: 'Depois de confirmar sua solicitação com a corretora de origem acompanhe pela B3 a conclusão da transferência!',
            ),

            // Vídeo
            const SizedBox(height: 40),
            const Text('Assista ao vídeo e conheça a nova STVM', style: AppTheme.bold16White),
            const SizedBox(height: 16),
            player,
          ],
        );
      },
    );
  }
}

class _StepItem extends StatelessWidget {
  final int number;
  final String text;

  const _StepItem({
    required this.number,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 42,
            height: 42,
            decoration: const BoxDecoration(color: AppTheme.blueColor6, shape: BoxShape.circle),
            child: Center(child: Text(number.toString(), style: AppTheme.bold16White)),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(text, style: AppTheme.regular14White)),
        ],
      ),
    );
  }
}
