import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/extensions.dart';
import '../widgets/input/input.dart';

abstract class AppTheme {
  static const Color blackColor = Color(0xFF000000);
  static const Color blueColor = Color(0xFF00528E);
  static const Color blueColor2 = Color(0xFF224E6B);
  static const Color blueColor3 = Color(0xFF01477A);
  static const Color blueColor4 = Color(0xFF072740);
  static const Color transparentBlueColor4 = Color(0x10072740);
  static const Color blueColor5 = Color(0xFF0463A9);
  static const Color blueColor6 = Color(0xFF005192);
  static const Color blueColor7 = Color(0xFF0081DF);
  static const Color blueColor8 = Color(0xFF00335E);
  static const Color blueColorTextBg = Color(0xFF009EFF);
  static const Color blueColorCardBg = Color(0xFF032B48);
  static const Color blueColorCardBg2 = Color(0xFF163146);
  static const Color blueColorCardBg3 = Color(0x3392989B);
  static const Color blueColorCardBg4 = Color(0xFF92989B);
  static const Color blueColorNew = Color(0xFF051049);
  static const Color blueColorNew2 = Color(0xFF052F4F);
  static const Color greyColor = Color(0xFF696969);
  static const Color greyColor2 = Color(0xFF94AFB6);
  static const Color greyColor3 = Color(0xFFCDE1EC);
  static const Color greyColor4 = Color(0xFF50595E);
  static const Color lightBlueColor = Color(0xFF0073BA);
  static const Color darkColor = Color(0xFF17191E);
  static const Color lightGreyColor = Color(0xFFEFEFEF);
  static const Color pinkColor = Color(0xFFED3DD1);
  static const Color orangeColor = Color(0xFFFF7E40);
  static const Color darkOrangeColor = Color(0xFFCC5A23);
  static const Color redColor = Color(0xFFD02323);
  static const Color redColor2 = Color(0xFFFF0004);
  static const Color redColor3 = Color(0xFFFC5555);
  static const Color redColor4 = Color(0xFFD74547);
  static const Color errorColor = Color(0xFFFF7575);
  static const Color inactiveColor = Color(0xFFF4484B);
  static const Color whiteColor = Color(0xFFFDFDFD);
  static const Color transparentWhiteColor = Color(0x20FDFDFD);
  static const Color greenColor = Color(0xFF12AC54);
  static const Color greenColor2 = Color(0xFF1DBC60);
  static const Color greenColor3 = Color(0xFF1CFF33);
  static const Color yellowColor = Color(0xFFFFC400);
  static const Color yellowColor2 = Color(0xFFFFAF1C);

  static const defaultText = TextStyle(height: 1.5, fontSize: 14, fontWeight: FontWeight.normal, decoration: TextDecoration.none, color: whiteColor);

  static const regular09Orange = TextStyle(fontSize: 9, color: orangeColor);
  static const regular10White = TextStyle(fontSize: 10, color: whiteColor);
  static const regular10Grey = TextStyle(fontSize: 10, color: greyColor);
  static const regular10BlueColorCardBg4 = TextStyle(fontSize: 10, color: blueColorCardBg4);
  static const regular11White = TextStyle(fontSize: 11, color: whiteColor);
  static const regular11Grey = TextStyle(fontSize: 11, color: greyColor);
  static const regular11Orange = TextStyle(fontSize: 11, color: orangeColor);
  static const regular12Error = TextStyle(fontSize: 12, color: errorColor);
  static const regular12Grey2 = TextStyle(fontSize: 12, color: greyColor2);
  static const regular12Grey3 = TextStyle(fontSize: 12, color: greyColor3);
  static const regular12Grey4 = TextStyle(fontSize: 12, color: greyColor4);
  static const regular12White = TextStyle(fontSize: 12, color: whiteColor);
  static const regular12Black = TextStyle(fontSize: 12, color: blackColor);
  static const regular12BlueTextBg = TextStyle(fontSize: 12, color: blueColorTextBg);
  static const regular12Orange = TextStyle(fontSize: 12, color: orangeColor);
  static const regular12Yellow = TextStyle(fontSize: 12, color: yellowColor);
  static const regular12Yellow2 = TextStyle(fontSize: 12, color: yellowColor2);
  static const regular13Orange = TextStyle(fontSize: 13, color: orangeColor);
  static const regular14Black = TextStyle(fontSize: 14, color: blackColor);
  static const regular14White = TextStyle(fontSize: 14, color: whiteColor);
  static const regular14Grey = TextStyle(fontSize: 14, color: greyColor);
  static const regular14Grey2 = TextStyle(fontSize: 14, color: greyColor2);
  static const regular14Grey3 = TextStyle(fontSize: 14, color: greyColor3);
  static const regular14Orange = TextStyle(fontSize: 14, color: orangeColor);
  static const regular14Yellow2 = TextStyle(fontSize: 14, color: yellowColor2);
  static const regular14Blue = TextStyle(fontSize: 14, color: blueColor8);
  static const regular15Black = TextStyle(fontSize: 15, color: blackColor);
  static const regular15White = TextStyle(fontSize: 15, color: whiteColor);
  static const regular16Black = TextStyle(fontSize: 16, color: blackColor);
  static const regular16BlueLight = TextStyle(fontSize: 16, color: lightBlueColor);
  static const regular16Grey2 = TextStyle(fontSize: 16, color: greyColor2);
  static const regular16Orange = TextStyle(fontSize: 16, color: orangeColor);
  static const regular16White = TextStyle(fontSize: 16, color: whiteColor);
  static const regular18White = TextStyle(fontSize: 18, color: whiteColor);

  static const medium14Grey = TextStyle(fontSize: 14, color: greyColor, fontWeight: AppFontWeight.medium);
  static const medium14Grey2 = TextStyle(fontSize: 14, color: greyColor2, fontWeight: AppFontWeight.medium);
  static const medium14Orange = TextStyle(fontSize: 14, color: orangeColor, fontWeight: AppFontWeight.medium);
  static const medium14Red = TextStyle(fontSize: 14, color: redColor, fontWeight: AppFontWeight.medium);
  static const medium14White = TextStyle(fontSize: 14, color: whiteColor, fontWeight: AppFontWeight.medium);
  static const medium20White = TextStyle(fontSize: 20, color: whiteColor, fontWeight: AppFontWeight.medium);

  static const semi10Black = TextStyle(fontSize: 10, color: blackColor, fontWeight: AppFontWeight.semiBold);
  static const semi10White = TextStyle(fontSize: 10, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi11White = TextStyle(fontSize: 11, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi12White = TextStyle(fontSize: 12, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi12Black = TextStyle(fontSize: 12, color: blackColor, fontWeight: AppFontWeight.semiBold);
  static const semi12Blue = TextStyle(fontSize: 12, color: blueColor, fontWeight: AppFontWeight.semiBold);
  static const semi12Orange = TextStyle(fontSize: 12, color: orangeColor, fontWeight: AppFontWeight.semiBold);
  static const semi14White = TextStyle(fontSize: 14, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi14Black = TextStyle(fontSize: 14, color: blackColor, fontWeight: AppFontWeight.semiBold);
  static const semi14blueColorTextBg = TextStyle(fontSize: 14, color: blueColorTextBg, fontWeight: AppFontWeight.semiBold);
  static const semi14BlueLight = TextStyle(fontSize: 14, color: lightBlueColor, fontWeight: AppFontWeight.semiBold);
  static const semi14BlueNew = TextStyle(fontSize: 14, color: blueColorNew, fontWeight: AppFontWeight.semiBold);
  static const semi14Orange = TextStyle(fontSize: 14, color: orangeColor, fontWeight: AppFontWeight.semiBold);
  static const semi14Red = TextStyle(fontSize: 14, color: redColor2, fontWeight: AppFontWeight.semiBold);
  static const semi14Green2 = TextStyle(fontSize: 14, color: greenColor, fontWeight: AppFontWeight.semiBold);

  static const semi15Black = TextStyle(fontSize: 15, color: blackColor, fontWeight: AppFontWeight.semiBold);
  static const semi15White = TextStyle(fontSize: 15, color: whiteColor, fontWeight: AppFontWeight.semiBold);

  static const medium12White = TextStyle(fontSize: 12, color: whiteColor, fontWeight: AppFontWeight.medium);
  static const medium16Black = TextStyle(fontSize: 16, color: blackColor, fontWeight: AppFontWeight.medium);
  static const medium16Blue = TextStyle(fontSize: 16, color: blueColor8, fontWeight: AppFontWeight.medium);
  static const semi16Black = TextStyle(fontSize: 16, color: blackColor, fontWeight: AppFontWeight.semiBold);
  static const semi16White = TextStyle(fontSize: 16, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi16Orange = TextStyle(fontSize: 16, color: orangeColor, fontWeight: AppFontWeight.semiBold);

  static const semi18White = TextStyle(fontSize: 18, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi24White = TextStyle(fontSize: 24, color: whiteColor, fontWeight: AppFontWeight.semiBold);
  static const semi32White = TextStyle(fontSize: 32, color: whiteColor, fontWeight: AppFontWeight.semiBold);

  static const bold10CardBg4 = TextStyle(fontSize: 10, color: blueColorCardBg4, fontWeight: AppFontWeight.bold);
  static const bold10Green3 = TextStyle(fontSize: 10, color: greenColor3, fontWeight: AppFontWeight.bold);
  static const bold11White = TextStyle(fontSize: 11, color: whiteColor, fontWeight: AppFontWeight.bold);
  static const bold11Blue8 = TextStyle(fontSize: 11, color: blueColor8, fontWeight: AppFontWeight.bold);
  static const bold12Black = TextStyle(fontSize: 12, color: blackColor, fontWeight: AppFontWeight.bold);
  static const bold12Orange = TextStyle(fontSize: 12, color: orangeColor, fontWeight: AppFontWeight.bold);
  static const bold12White = TextStyle(fontSize: 12, color: whiteColor, fontWeight: AppFontWeight.bold);
  static const bold12Grey = TextStyle(fontSize: 12, color: greyColor, fontWeight: AppFontWeight.bold);
  static const bold14White = TextStyle(fontSize: 14, color: whiteColor, fontWeight: AppFontWeight.bold);
  static const bold14Orange = TextStyle(fontSize: 14, color: orangeColor, fontWeight: AppFontWeight.bold);
  static const bold16Grey3 = TextStyle(fontSize: 16, color: greyColor3, fontWeight: AppFontWeight.bold);
  static const bold16Blue = TextStyle(fontSize: 16, color: blueColor8, fontWeight: AppFontWeight.bold);

  static const bold16Green = TextStyle(fontSize: 16, color: greenColor3, fontWeight: AppFontWeight.bold);
  static const bold16Orange = TextStyle(fontSize: 16, color: orangeColor, fontWeight: AppFontWeight.bold);
  static const bold16White = TextStyle(fontSize: 16, color: whiteColor, fontWeight: AppFontWeight.bold);
  static const bold16Black = TextStyle(fontSize: 16, color: blackColor, fontWeight: AppFontWeight.bold);
  static const bold16Blue6 = TextStyle(fontSize: 16, color: blueColor6, fontWeight: AppFontWeight.bold);
  static const bold16Gray = TextStyle(fontSize: 16, color: blueColorCardBg4, fontWeight: AppFontWeight.bold);

  static const bold18Black = TextStyle(fontSize: 18, color: blackColor, fontWeight: AppFontWeight.bold);
  static const bold18White = TextStyle(fontSize: 18, color: whiteColor, fontWeight: AppFontWeight.bold);

  static const bold20White = TextStyle(fontSize: 20, color: whiteColor, fontWeight: AppFontWeight.bold);

  static const bold24White = TextStyle(fontSize: 24, color: whiteColor, fontWeight: AppFontWeight.bold);
  static const bold24Black = TextStyle(fontSize: 24, color: blackColor, fontWeight: AppFontWeight.bold);
  static const bold24Grey = TextStyle(fontSize: 24, color: greyColor, fontWeight: AppFontWeight.bold);

  static const bold29White = TextStyle(fontSize: 29, color: whiteColor, fontWeight: AppFontWeight.bold, height: 1.1);
  static const bold47White = TextStyle(fontSize: 47, color: whiteColor, fontWeight: AppFontWeight.bold, height: 1.2);

  static const bold33White = TextStyle(fontSize: 33, color: whiteColor, fontWeight: AppFontWeight.bold, height: 1.1);

  static const extraBold18White = TextStyle(fontSize: 18, fontWeight: AppFontWeight.extraBold);

  static TextStyle smallText({
    FontWeight fontWeight = FontWeight.normal,
    Color color = blackColor,
    bool hasHeight = true,
    double fontSize = 12,
  }) =>
      TextStyle(
        color: color,
        fontSize: fontSize,
        height: hasHeight ? 1.5 : null,
        fontWeight: fontWeight,
      );

  static BoxDecoration cardBoxDecoration = BoxDecoration(
    borderRadius: BorderRadius.circular(5),
    color: blueColorCardBg2,
  );

  static BoxDecoration cardWithBorderBoxDecoration = BoxDecoration(
    color: blueColorCardBg,
    border: Border.all(
      color: Colors.white.withOpacity(0.4),
      width: 0.8,
    ),
    borderRadius: BorderRadius.circular(5),
  );

  static const List<BoxShadow> boxShadowBlack = [
    BoxShadow(
      color: Colors.black26,
      blurRadius: 4,
      offset: Offset(0, 4),
    ),
  ];

  static const EdgeInsets horizontalPadding = EdgeInsets.symmetric(horizontal: 25);
  static const EdgeInsets pagePadding = EdgeInsets.fromLTRB(25, 20, 25, 80);
  static const EdgeInsets cardPadding = EdgeInsets.all(15);
  static const EdgeInsets cardMargin = EdgeInsets.fromLTRB(20, 10, 20, 10);

  static const double filledInputLabelPaddingBottom = 5;
  static const EdgeInsets signupFieldPadding = EdgeInsets.only(bottom: 15);

  static final SystemUiOverlayStyle systemTheme = SystemUiOverlayStyle.light.copyWith(
    statusBarColor: Colors.transparent,
    systemNavigationBarColor: blueColor4,
  );

  static final themeData = ThemeData(
    fontFamily: 'Open Sans',
    colorScheme: ColorScheme.fromSwatch(
      primarySwatch: blueColor.toMaterialColor(),
      accentColor: orangeColor,
      errorColor: errorColor,
      backgroundColor: whiteColor,
      brightness: Brightness.dark,
    ),
    visualDensity: VisualDensity.adaptivePlatformDensity,
    textTheme: const TextTheme(
      bodyMedium: regular14White,
      bodyLarge: regular16White,
    ).apply(
      bodyColor: whiteColor,
      displayColor: whiteColor,
    ),
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: orangeColor,
    ),
    scrollbarTheme: ScrollbarThemeData(
      thumbColor: MaterialStateProperty.all(orangeColor),
      crossAxisMargin: 4,
      thickness: MaterialStateProperty.all(6),
      radius: const Radius.circular(10),
    ),
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      border: underlineBorderDefault,
      enabledBorder: underlineBorderDefault,
      focusedBorder: underlineBorderDefault.copyWith(
        borderSide: const BorderSide(color: orangeColor, width: 2),
      ),
      errorBorder: underlineBorderDefault,
      focusedErrorBorder: underlineBorderError.copyWith(
        borderSide: const BorderSide(color: errorColor, width: 2),
      ),
      labelStyle: Input.inputLabelStyle,
      helperStyle: const TextStyle(color: whiteColor),
      hintStyle: const TextStyle(color: Colors.white54),
      errorStyle: const TextStyle(color: errorColor),
    ),
  );
}

abstract class AppFontWeight {
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight regular = FontWeight.normal;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight medium = FontWeight.w500;
}
