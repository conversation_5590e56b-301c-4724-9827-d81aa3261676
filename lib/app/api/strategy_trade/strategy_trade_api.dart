import 'package:get/get.dart';

import '../../models/trade/strategies_response.dart';
import '../../models/trade/strategy_history.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_open_position.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../trade/trade_api.dart';

class StrategyTradeApi {
  final _tradeApi = Get.find<TradeApi>();

  Future<Trade> getStrategyById(String id) {
    return _tradeApi.getTradeById(path: '/TradeQuantitativo/estrategia/$id');
  }

  Future<StrategiesResponse> getStrategies() async {
    final response = await _tradeApi.getTrades(path: '/TradeQuantitativo/estrategias');
    return StrategiesResponse.fromJson(response.data);
  }

  Future<StrategyHistory> getStrategyHistory({required int strategyId, required DateTime monthDate}) async {
    final response = await _tradeApi.getTradeHistory(path: '/TradeQuantitativo/historico/$strategyId', monthDate: monthDate);
    return StrategyHistory.fromJson(response.data);
  }

  Future<void> hireStrategy({
    required int strategyId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeQuantitativo/$strategyId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
    );
  }

  Future<void> editStrategy({
    required int strategyId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeQuantitativo/editar/$strategyId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
    );
  }

  Future<void> deactivateStrategy({
    required int strategyId,
    required List<List<int>> signature,
  }) {
    return _tradeApi.deactivateTrade(
      path: '/TradeQuantitativo/desativar/$strategyId',
      signature: signature,
    );
  }

  Future<TradeOpenPositionsResponse> getStrategyOpenPositions({required int strategyId}) {
    return _tradeApi.getOpenPositions(path: '/TradeQuantitativo/operacoesEmAberto/$strategyId');
  }

  Future<TradeClosedPositionsResponse> getStrategyClosedPositions({
    required int strategyId,
    required DateTime monthDate,
  }) {
    return _tradeApi.getClosedPositions(path: '/TradeQuantitativo/operacoesEncerradas/$strategyId', monthDate: monthDate);
  }

  Future<TradeRequiredBalanceCalculation> getStrategyRequiredBalanceForAmount({
    required int strategyId,
    required double amount,
    bool isActive = false,
  }) {
    return _tradeApi.getRequiredBalanceForAmount(
      path: '/TradeQuantitativo/obterValorGarantia',
      tradeIdEntry: MapEntry('estrategiaId', strategyId),
      amount: amount,
      isActive: isActive,
    );
  }
}
