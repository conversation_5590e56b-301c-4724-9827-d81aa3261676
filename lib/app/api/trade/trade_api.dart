import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_open_position.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../../utils/secret.dart';
import '../cm_request.dart';

class TradeApi {
  final _request = Get.find<CMRequest>();

  Future<Trade> getTradeById({required String path}) async {
    final response = await _request.get(path);
    return Trade.fromJson(response.data);
  }

  Future<dio.Response> getTrades({required String path}) {
    return _request.get(path);
  }

  Future<dio.Response> getTradeHistory({required String path, required DateTime monthDate}) {
    return _request.get(
      path,
      queryParameters: {
        'mes': monthDate.month,
        'ano': monthDate.year,
      },
      encryptionOptions: const EncryptionOptions.noEncryption(),
    );
  }

  Future<void> hireTrade({
    required String path,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) async {
    final data = {
      'valorPorRecomendacao': amount,
      'assinatura': getSecretPreEncryptionString(signature),
      'aceiteTermo': didAcceptTerms,
      'desenquadrado': isProfileSuitabilityUnfit,
    };
    if (contractCount != null) data['quantidadeDeContrato'] = contractCount;
    await _request.post(path, data, encryptionOptions: const EncryptionOptions.values({'assinatura': true}));
  }

  Future<void> editTrade({
    required String path,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) async {
    final data = {
      'valorPorRecomendacao': amount,
      'assinatura': getSecretPreEncryptionString(signature),
      'aceiteTermo': didAcceptTerms,
      'desenquadrado': isProfileSuitabilityUnfit,
    };
    if (contractCount != null) data['quantidadeDeContrato'] = contractCount;
    await _request.put(path, data, encryptionOptions: const EncryptionOptions.values({'assinatura': true}));
  }

  Future<void> deactivateTrade({
    required String path,
    required List<List<int>> signature,
  }) async {
    await _request.put(
      path,
      {
        'assinatura': getSecretPreEncryptionString(signature),
      },
      encryptionOptions: const EncryptionOptions.values({'assinatura': true}),
    );
  }

  Future<TradeOpenPositionsResponse> getOpenPositions({required String path}) async {
    final response = await _request.get(path);
    return TradeOpenPositionsResponse.fromJson(response.data);
  }

  Future<TradeClosedPositionsResponse> getClosedPositions({required String path, required DateTime monthDate}) async {
    final response = await _request.get(
      path,
      queryParameters: {
        'mes': monthDate.month,
        'ano': monthDate.year,
      },
      encryptionOptions: const EncryptionOptions.noEncryption(),
    );
    return TradeClosedPositionsResponse.fromJson(response.data);
  }

  Future<TradeRequiredBalanceCalculation> getRequiredBalanceForAmount({
    required String path,
    required MapEntry<String, int> tradeIdEntry,
    required double amount,
    bool isActive = false,
  }) async {
    final data = <String, dynamic>{
      'valorPorRecomendacao': amount,
      'tipoOperacao': isActive ? 2 : 1, // 1 - Ativação, 2 - Edição
    }..addEntries([tradeIdEntry]);
    final response = await _request.post(path, data, encryptionOptions: const EncryptionOptions.noEncryption());
    return TradeRequiredBalanceCalculation.fromJson(response.data);
  }
}
