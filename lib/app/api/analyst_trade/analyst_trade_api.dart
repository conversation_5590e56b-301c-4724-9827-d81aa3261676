import 'package:get/get.dart';

import '../../models/trade/analyst_history.dart';
import '../../models/trade/analysts_response.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_open_position.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../trade/trade_api.dart';

class AnalystTradeApi {
  final _tradeApi = Get.find<TradeApi>();

  Future<Trade> getAnalystById(String id) {
    return _tradeApi.getTradeById(path: '/TradeAnalista/analista/$id');
  }

  Future<AnalystsResponse> getAnalysts() async {
    final response = await _tradeApi.getTrades(path: '/TradeAnalista/analistas');
    return AnalystsResponse.fromJson(response.data);
  }

  Future<AnalystHistory> getAnalystHistory({required int analystId, required DateTime monthDate}) async {
    final response = await _tradeApi.getTradeHistory(path: '/TradeAnalista/historico/$analystId', monthDate: monthDate);
    return AnalystHistory.fromJson(response.data);
  }

  Future<void> hireAnalyst({
    required int analystId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.hireTrade(
      path: '/TradeAnalista/$analystId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> editAnalyst({
    required int analystId,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _tradeApi.editTrade(
      path: '/TradeAnalista/editar/$analystId',
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  Future<void> deactivateAnalyst({
    required int analystId,
    required List<List<int>> signature,
  }) {
    return _tradeApi.deactivateTrade(
      path: '/TradeAnalista/desativar/$analystId',
      signature: signature,
    );
  }

  Future<TradeOpenPositionsResponse> getAnalystOpenPositions({required int analystId}) {
    return _tradeApi.getOpenPositions(path: '/TradeAnalista/operacoesEmAberto/$analystId');
  }

  Future<TradeClosedPositionsResponse> getAnalystClosedPositions({
    required int analystId,
    required DateTime monthDate,
  }) {
    return _tradeApi.getClosedPositions(path: '/TradeAnalista/operacoesEncerradas/$analystId', monthDate: monthDate);
  }

  Future<TradeRequiredBalanceCalculation> getAnalystRequiredBalanceForAmount({
    required int analystId,
    required double amount,
    bool isActive = false,
  }) {
    return _tradeApi.getRequiredBalanceForAmount(
      path: '/TradeAnalista/obterValorGarantia',
      tradeIdEntry: MapEntry('analistaId', analystId),
      amount: amount,
      isActive: isActive,
    );
  }
}
