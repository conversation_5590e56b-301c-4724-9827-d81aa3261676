// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'portability_asset.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PortabilityAsset _$PortabilityAssetFromJson(Map<String, dynamic> json) =>
    PortabilityAsset(
      id: (json['id'] as num?)?.toInt(),
      type: json['type'] as String?,
      name: json['name'] as String?,
      availableQuantity: (json['availableQuantity'] as num?)?.toDouble(),
      selectedQuantity: (json['selectedQuantity'] as num?)?.toDouble(),
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$PortabilityAssetToJson(PortabilityAsset instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'name': instance.name,
      'availableQuantity': instance.availableQuantity,
      'selectedQuantity': instance.selectedQuantity,
      'isSelected': instance.isSelected,
    };
