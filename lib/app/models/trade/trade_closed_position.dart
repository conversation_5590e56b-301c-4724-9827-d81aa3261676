import 'package:json_annotation/json_annotation.dart';

import 'trade_position.dart';

part 'trade_closed_position.g.dart';

@JsonSerializable(createToJson: false)
class TradeClosedPositionsResponse {
  @Json<PERSON>ey(name: 'operacoesencerradas')
  final List<TradeClosedPosition> positions;

  @Json<PERSON>ey(name: 'totalvaloraplicado')
  final double? totalMonthInvested;

  @<PERSON>son<PERSON>ey(name: 'totalresultadomesvalor')
  final double? totalMonthResult;

  @Json<PERSON>ey(name: 'totalresultadomesporcentagem')
  final double? totalMonthResultPercentage;

  TradeClosedPositionsResponse({
    this.positions = const [],
    this.totalMonthInvested,
    this.totalMonthResult,
    this.totalMonthResultPercentage,
  });

  factory TradeClosedPositionsResponse.fromJson(Map<String, dynamic> json) => _$TradeClosedPositionsResponseFromJson(json);
}

@JsonSerializable(createToJson: false)
class TradeClosedPosition extends TradePosition {
  @Json<PERSON><PERSON>(name: 'dataoperacao')
  final DateTime? startDate;

  @J<PERSON><PERSON><PERSON>(name: 'saldosuficiente')
  final bool hadEnoughBalance;

  @JsonKey(name: 'saidaantecipada')
  final bool wasClosedEarly;

  @JsonKey(name: 'valoraplicado')
  final double? invested;

  @JsonKey(name: 'dataencerramento')
  final DateTime? endDate;

  @JsonKey(name: 'gainloss')
  final double? resultPercentage;

  @JsonKey(name: 'resultado')
  final double? resultPrice;

  @JsonKey(name: 'numerodecontratos')
  final int? numberOfContracts;

  TradeClosedPosition({
    super.id,
    this.startDate,
    super.ticker,
    this.hadEnoughBalance = false,
    this.wasClosedEarly = false,
    this.invested,
    this.endDate,
    this.resultPercentage,
    this.resultPrice,
    this.numberOfContracts,
    super.positionType,
  });

  factory TradeClosedPosition.fromJson(Map<String, dynamic> json) => _$TradeClosedPositionFromJson(json);
}
