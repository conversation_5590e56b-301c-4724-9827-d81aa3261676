import 'dart:math';

import 'package:json_annotation/json_annotation.dart';

import '../../config/constants.dart';

part 'trade.g.dart';

@JsonSerializable(createToJson: false)
class Trade {
  final int? id;

  @Json<PERSON><PERSON>(name: 'nome')
  final String? firstName;

  @Json<PERSON>ey(name: 'sobrenome')
  final String? lastName;

  @Json<PERSON><PERSON>(name: 'fotoweb')
  final String? webImageUrl;

  @Json<PERSON><PERSON>(name: 'fotoapp')
  final String? mobileImageUrl;

  @Json<PERSON>ey(name: 'descricao')
  final String? fullDescription;

  @JsonKey(name: 'descricaoestrategia')
  final String? shortDescription;

  @Json<PERSON>ey(name: 'rendimento')
  final double? performancePercentage;

  @JsonKey(name: 'taxadeacerto')
  final double? successRate;

  @JsonKey(name: 'mediaporrecomendacoes')
  final double? recommendationAverage;

  @JsonKey(name: 'mesesatividade', fromJson: _parseDateFromYearAndMonthOnly)
  final List<DateTime> monthsWithActivity;

  @JsonKey(name: 'dataprimeirarecomendacao')
  final DateTime? firstActivityDate;

  @Json<PERSON>ey(name: 'valorminimo')
  final double? minValuePerOperation;

  @JsonKey(name: 'valoroperacao')
  final double? hiredValuePerOperation;

  @JsonKey(name: 'contratado')
  final bool isActive;

  @JsonKey(name: 'relatorios')
  final List<TradeReport> reports;

  @JsonKey(name: 'linkurl')
  final String? videoUrl;

  ///Bovespa = 1
  ///Mercado Futuro = 2
  ///Não atualizado = 0
  @JsonKey(name: 'tipomercado')
  final int? market;

  @JsonKey(name: 'numerodecontratos')
  final int? numberOfContracts;

  bool isHired;

  final int minimumNumberOfContracts;

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}';

  String get profileImageUrl => mobileImageUrl ?? webImageUrl ?? '';

  bool get hasHistory => monthsWithActivity.isNotEmpty;

  bool get isBmf => market == 2;

  Trade({
    this.id,
    this.firstName,
    this.lastName,
    this.webImageUrl,
    this.mobileImageUrl,
    this.fullDescription,
    this.shortDescription,
    this.performancePercentage,
    this.successRate,
    this.recommendationAverage,
    this.monthsWithActivity = const [],
    this.firstActivityDate,
    this.hiredValuePerOperation,
    this.minValuePerOperation,
    this.isActive = false,
    this.reports = const [],
    this.isHired = false,
    this.videoUrl,
    this.market,
    this.numberOfContracts,
  }) : minimumNumberOfContracts = (minValuePerOperation ?? 0) ~/ Constants.tradeBmfContractValue;

  /// Faz o parse de datas no formato 'MM/yyyy' para DateTime.
  static List<DateTime> _parseDateFromYearAndMonthOnly(List? yearsAndMonths) {
    return (yearsAndMonths ?? []).map((yearAndMonth) {
      final parts = yearAndMonth.toString().split('/');
      return DateTime(int.parse(parts[1]), int.parse(parts[0]));
    }).toList();
  }

  factory Trade.fromJson(Map<String, dynamic> json) => _$TradeFromJson(json);
}

@JsonSerializable(createToJson: false)
class TradeReport {
  @JsonKey(name: 'titulo')
  final String? title;
  @JsonKey(name: 'dataPublicacao')
  final DateTime? date;
  @JsonKey(name: 'link')
  final String? url;

  TradeReport({this.title, this.date, this.url});

  factory TradeReport.fromJson(Map<String, dynamic> json) => _$TradeReportFromJson(json);
}

extension SortByPerformance on List<Trade> {
  /// Ordena por maior [performancePercentage].
  List<Trade> get sortedByPerformance {
    return this..sort((a, b) => (b.performancePercentage ?? 0).compareTo(a.performancePercentage ?? 0));
  }

  /// Retorna os trades com [performancePercentage] positivo ou zero.
  List<Trade> get positivePerformance {
    return where((trade) => (trade.performancePercentage ?? 0) >= 0).toList();
  }

  /// Retorna os trades com [performancePercentage] negativo.
  List<Trade> get negativePerformance {
    return where((trade) => (trade.performancePercentage ?? 0) < 0).toList();
  }

  /// Alterna entre Mercado à Vista e Mercado Futuro.
  List<Trade> get alteranatedByMarket {
    // Separa os trades por tipo de mercado
    final spotTrades = where((trade) => !trade.isBmf).toList();
    if (spotTrades.isEmpty) return this;
    final futuresTrades = where((trade) => trade.isBmf).toList();
    if (futuresTrades.isEmpty) return this;

    // Alterna os trades por tipo de mercado, começando por BMF
    List<Trade> result = [];
    for (var index = 0; index < max(spotTrades.length, futuresTrades.length); index++) {
      if (index < spotTrades.length) result.add(spotTrades[index]);
      if (index < futuresTrades.length) result.add(futuresTrades[index]);
    }
    return result;
  }
}
