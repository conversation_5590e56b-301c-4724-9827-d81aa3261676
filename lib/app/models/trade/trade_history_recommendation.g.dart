// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_history_recommendation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradeHistoryRecommendation _$TradeHistoryRecommendationFromJson(
        Map<String, dynamic> json) =>
    TradeHistoryRecommendation(
      id: (json['id'] as num?)?.toDouble(),
      startDate: json['datarecomendacao'] == null
          ? null
          : DateTime.parse(json['datarecomendacao'] as String),
      endDate: json['dataencerramento'] == null
          ? null
          : DateTime.parse(json['dataencerramento'] as String),
      entryPrice: (json['entrada'] as num?)?.toDouble(),
      exitPrice: (json['saida'] as num?)?.toDouble(),
      ticker: json['ticker'] as String?,
      resultStatus: json['resultadotipo'] as String?,
      resultPercentage: (json['resultadoporcentagem'] as num?)?.toDouble(),
      positionType:
          TradePositionType.fromId((json['compravenda'] as num?)?.toInt()),
    );
