import 'package:json_annotation/json_annotation.dart';

import 'trade_position.dart';

part 'trade_open_position.g.dart';

@JsonSerializable(createToJson: false)
class TradeOpenPositionsResponse {
  @Json<PERSON>ey(name: 'operacoesabertas')
  List<TradeOpenPosition> positions;

  TradeOpenPositionsResponse({
    this.positions = const [],
  });

  factory TradeOpenPositionsResponse.fromJson(Map<String, dynamic> json) => _$TradeOpenPositionsResponseFromJson(json);
}

@JsonSerializable(createToJson: false)
class TradeOpenPosition extends TradePosition {
  @Json<PERSON><PERSON>(name: 'dataoperacao')
  final DateTime? startDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'valoraplicado')
  final double? invested;

  @JsonKey(name: 'entrada')
  final double? entryPrice;

  @Json<PERSON>ey(name: 'gain')
  final double? gainPrice;

  @Json<PERSON>ey(name: 'loss')
  final double? lossPrice;

  @JsonKey(name: 'numerodecontratos')
  final int? numberOfContracts;

  TradeOpenPosition({
    super.id,
    this.startDate,
    super.ticker,
    this.entryPrice,
    this.gainPrice,
    this.lossPrice,
    this.invested,
    this.numberOfContracts,
    super.positionType,
  });

  factory TradeOpenPosition.fromJson(Map<String, dynamic> json) => _$TradeOpenPositionFromJson(json);
}
