import 'package:json_annotation/json_annotation.dart';

import 'trade_history.dart';
import 'trade_history_recommendation.dart';

part 'strategy_history.g.dart';

@JsonSerializable(createToJson: false)
class StrategyHistory extends TradeHistory {
  @override
  @JsonKey(name: 'historicoestrategia')
  final List<TradeHistoryRecommendation> recommendations;

  StrategyHistory({
    super.totalMonthResult,
    super.yearPerformancePercentage,
    this.recommendations = const [],
  });

  factory StrategyHistory.fromJson(Map<String, dynamic> json) => _$StrategyHistoryFromJson(json);
}
