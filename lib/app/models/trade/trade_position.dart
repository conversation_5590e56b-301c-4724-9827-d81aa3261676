import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable(createFactory: false, createToJson: false)
class TradePosition {
  final double? id;

  final String? ticker;

  @<PERSON>son<PERSON>ey(name: 'compravenda', fromJson: TradePositionType.fromId)
  final TradePositionType? positionType;

  TradePosition({this.id, this.ticker, this.positionType});
}

enum TradePositionType {
  long(1, 'Co<PERSON>ra'),
  short(2, 'Venda');

  final int id;
  final String description;

  const TradePositionType(this.id, this.description);

  static TradePositionType? fromId(int? id) => TradePositionType.values.firstWhereOrNull((operation) => operation.id == id);
}
