import 'package:json_annotation/json_annotation.dart';

import 'trade.dart';

part 'analysts_response.g.dart';

@JsonSerializable(createToJson: false)
class AnalystsResponse {
  @JsonKey(name: 'analistasdisponiveis')
  final List<Trade> availableAnalysts;

  @JsonKey(name: 'analistascontratados')
  final List<Trade> hiredAnalysts;

  AnalystsResponse({
    this.availableAnalysts = const [],
    this.hiredAnalysts = const [],
  });

  factory AnalystsResponse.fromJson(Map<String, dynamic> json) => _$AnalystsResponseFromJson(json);
}
