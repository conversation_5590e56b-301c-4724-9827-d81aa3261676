// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_open_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradeOpenPositionsResponse _$TradeOpenPositionsResponseFromJson(
        Map<String, dynamic> json) =>
    TradeOpenPositionsResponse(
      positions: (json['operacoesabertas'] as List<dynamic>?)
              ?.map(
                  (e) => TradeOpenPosition.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

TradeOpenPosition _$TradeOpenPositionFromJson(Map<String, dynamic> json) =>
    TradeOpenPosition(
      id: (json['id'] as num?)?.toDouble(),
      startDate: json['dataoperacao'] == null
          ? null
          : DateTime.parse(json['dataoperacao'] as String),
      ticker: json['ticker'] as String?,
      entryPrice: (json['entrada'] as num?)?.toDouble(),
      gainPrice: (json['gain'] as num?)?.toDouble(),
      lossPrice: (json['loss'] as num?)?.toDouble(),
      invested: (json['valoraplicado'] as num?)?.toDouble(),
      numberOfContracts: (json['numerodecontratos'] as num?)?.toInt(),
      positionType:
          TradePositionType.fromId((json['compravenda'] as num?)?.toInt()),
    );
