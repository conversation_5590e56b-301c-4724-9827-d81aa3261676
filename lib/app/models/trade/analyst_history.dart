import 'package:json_annotation/json_annotation.dart';

import 'trade_history.dart';
import 'trade_history_recommendation.dart';

part 'analyst_history.g.dart';

@JsonSerializable(createToJson: false)
class AnalystHistory extends TradeHistory {
  @override
  @JsonKey(name: 'historicoanalista')
  final List<TradeHistoryRecommendation> recommendations;

  AnalystHistory({
    super.totalMonthResult,
    super.yearPerformancePercentage,
    this.recommendations = const [],
  });

  factory AnalystHistory.fromJson(Map<String, dynamic> json) => _$AnalystHistoryFromJson(json);
}
