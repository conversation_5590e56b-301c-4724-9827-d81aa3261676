// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_closed_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradeClosedPositionsResponse _$TradeClosedPositionsResponseFromJson(
        Map<String, dynamic> json) =>
    TradeClosedPositionsResponse(
      positions: (json['operacoesencerradas'] as List<dynamic>?)
              ?.map((e) =>
                  TradeClosedPosition.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalMonthInvested: (json['totalvaloraplicado'] as num?)?.toDouble(),
      totalMonthResult: (json['totalresultadomesvalor'] as num?)?.toDouble(),
      totalMonthResultPercentage:
          (json['totalresultadomesporcentagem'] as num?)?.toDouble(),
    );

TradeClosedPosition _$TradeClosedPositionFromJson(Map<String, dynamic> json) =>
    TradeClosedPosition(
      id: (json['id'] as num?)?.toDouble(),
      startDate: json['dataoperacao'] == null
          ? null
          : DateTime.parse(json['dataoperacao'] as String),
      ticker: json['ticker'] as String?,
      hadEnoughBalance: json['saldosuficiente'] as bool? ?? false,
      wasClosedEarly: json['saidaantecipada'] as bool? ?? false,
      invested: (json['valoraplicado'] as num?)?.toDouble(),
      endDate: json['dataencerramento'] == null
          ? null
          : DateTime.parse(json['dataencerramento'] as String),
      resultPercentage: (json['gainloss'] as num?)?.toDouble(),
      resultPrice: (json['resultado'] as num?)?.toDouble(),
      numberOfContracts: (json['numerodecontratos'] as num?)?.toInt(),
      positionType:
          TradePositionType.fromId((json['compravenda'] as num?)?.toInt()),
    );
