import 'package:json_annotation/json_annotation.dart';

import 'trade_history_recommendation.dart';

part 'trade_history.g.dart';

@JsonSerializable(createToJson: false)
class TradeHistory {
  @JsonKey(name: 'resultadomes')
  final double? totalMonthResult;

  @JsonKey(name: 'rendimento')
  final double? yearPerformancePercentage;

  final List<TradeHistoryRecommendation> recommendations;

  TradeHistory({
    this.totalMonthResult,
    this.yearPerformancePercentage,
    this.recommendations = const [],
  });

  factory TradeHistory.fromJson(Map<String, dynamic> json) => _$TradeHistoryFromJson(json);
}
