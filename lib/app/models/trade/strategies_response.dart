import 'package:json_annotation/json_annotation.dart';

import 'trade.dart';

part 'strategies_response.g.dart';

@JsonSerializable(createToJson: false)
class StrategiesResponse {
  @JsonKey(name: 'estrategiasdisponiveis')
  final List<Trade> availableStrategies;

  @JsonKey(name: 'estrategiascontratados')
  final List<Trade> hiredStrategies;

  StrategiesResponse({
    this.availableStrategies = const [],
    this.hiredStrategies = const [],
  });

  factory StrategiesResponse.fromJson(Map<String, dynamic> json) => _$StrategiesResponseFromJson(json);
}
