import 'package:json_annotation/json_annotation.dart';

import 'trade_position.dart';

part 'trade_history_recommendation.g.dart';

@JsonSerializable(createToJson: false)
class TradeHistoryRecommendation extends TradePosition {
  @J<PERSON><PERSON><PERSON>(name: 'datarecomendacao')
  DateTime? startDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'dataencerramento')
  DateTime? endDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'entrada')
  double? entryPrice;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'saida')
  double? exitPrice;

  @Json<PERSON>ey(name: 'resultadotipo')
  String? resultStatus;

  @Json<PERSON>ey(name: 'resultadoporcentagem')
  double? resultPercentage;

  TradeHistoryRecommendation({
    super.id,
    this.startDate,
    this.endDate,
    this.entryPrice,
    this.exitPrice,
    super.ticker,
    this.resultStatus,
    this.resultPercentage,
    super.positionType,
  });

  factory TradeHistoryRecommendation.fromJson(Map<String, dynamic> json) => _$TradeHistoryRecommendationFromJson(json);
}
