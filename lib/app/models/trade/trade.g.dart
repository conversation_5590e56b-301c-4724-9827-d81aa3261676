// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Trade _$TradeFromJson(Map<String, dynamic> json) => Trade(
      id: (json['id'] as num?)?.toInt(),
      firstName: json['nome'] as String?,
      lastName: json['sobrenome'] as String?,
      webImageUrl: json['fotoweb'] as String?,
      mobileImageUrl: json['fotoapp'] as String?,
      fullDescription: json['descricao'] as String?,
      shortDescription: json['descricaoestrategia'] as String?,
      performancePercentage: (json['rendimento'] as num?)?.toDouble(),
      successRate: (json['taxadeacerto'] as num?)?.toDouble(),
      recommendationAverage:
          (json['mediaporrecomendacoes'] as num?)?.toDouble(),
      monthsWithActivity: json['mesesatividade'] == null
          ? const []
          : Trade._parseDateFromYearAndMonthOnly(
              json['mesesatividade'] as List?),
      firstActivityDate: json['dataprimeirarecomendacao'] == null
          ? null
          : DateTime.parse(json['dataprimeirarecomendacao'] as String),
      hiredValuePerOperation: (json['valoroperacao'] as num?)?.toDouble(),
      minValuePerOperation: (json['valorminimo'] as num?)?.toDouble(),
      isActive: json['contratado'] as bool? ?? false,
      reports: (json['relatorios'] as List<dynamic>?)
              ?.map((e) => TradeReport.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isHired: json['isHired'] as bool? ?? false,
      videoUrl: json['linkurl'] as String?,
      market: (json['tipomercado'] as num?)?.toInt(),
      numberOfContracts: (json['numerodecontratos'] as num?)?.toInt(),
    );

TradeReport _$TradeReportFromJson(Map<String, dynamic> json) => TradeReport(
      title: json['titulo'] as String?,
      date: json['dataPublicacao'] == null
          ? null
          : DateTime.parse(json['dataPublicacao'] as String),
      url: json['link'] as String?,
    );
