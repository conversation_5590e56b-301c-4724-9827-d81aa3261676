import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/transfers/transfers_api.dart';
import '../../models/transfer/portability_asset.dart';
import '../../pages/tabs/transfers/portability/portability_request_asset_selection_page.dart';
import '../../utils/extensions.dart';
import '../../models/transfer/exchange.dart';

class PortabilityToRequestController extends GetxController {
  final _transfersApi = Get.find<TransfersApi>();

  // Seleção de corretora
  final exchanges = <Exchange>[].obs;
  final exchangeFormKey = GlobalKey<FormState>();
  final exchangeFormKeyAutovalidateMode = AutovalidateMode.disabled.obs;
  final selectedExchange = Rx<Exchange?>(null);
  final sinacorCode = ''.obs;
  bool get canSubmit => selectedExchange.value != null && sinacorCode.value.isNotEmpty;

  Future<void> fetchExchanges() async {
    final exchangeList = await _transfersApi.getExchangeList();
    exchanges.assignAll(exchangeList);
  }

  Future<List<Exchange>> filterExchanges(String text) async {
    if (text.isEmpty) return exchanges;
    return exchanges.where((exchange) => (exchange.name ?? '').matchToSearch(text)).toList();
  }

  Future<void> onExchangeSubmitted() async {
    exchangeFormKeyAutovalidateMode.value = AutovalidateMode.always;
    if (exchangeFormKey.currentState?.validate() != true) return;
    Get.toNamed(PortabilityRequestAssetSelectionPage.routeName);
  }

  // Seleção de ativos
  final assets = <PortabilityAsset>[].obs;
  final assetFormKey = GlobalKey<FormState>();
  final assetFormKeyAutovalidateMode = AutovalidateMode.disabled.obs;

  Future<void> fetchAsstes() async {
    final assetList = await _transfersApi.getAssetList();
    assets.assignAll(assetList);
  }
}
