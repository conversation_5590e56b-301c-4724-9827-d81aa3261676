import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/transfers/transfers_api.dart';
import '../../models/transfer/portability_asset.dart';
import '../../pages/tabs/transfers/portability/portability_request_asset_selection_page.dart';
import '../../utils/extensions.dart';
import '../../models/transfer/exchange.dart';

class PortabilityToRequestController extends GetxController {
  final _transfersApi = Get.find<TransfersApi>();

  // Seleção de corretora
  final exchanges = <Exchange>[].obs;
  final exchangeFormKey = GlobalKey<FormState>();
  final exchangeFormKeyAutovalidateMode = AutovalidateMode.disabled.obs;
  final selectedExchange = Rx<Exchange?>(null);
  final sinacorCode = ''.obs;
  bool get canSubmit => selectedExchange.value != null && sinacorCode.value.isNotEmpty;

  Future<void> fetchExchanges() async {
    final exchangeList = await _transfersApi.getExchangeList();
    exchanges.assignAll(exchangeList);
  }

  Future<List<Exchange>> filterExchanges(String text) async {
    if (text.isEmpty) return exchanges;
    return exchanges.where((exchange) => (exchange.name ?? '').matchToSearch(text)).toList();
  }

  Future<void> onExchangeSubmitted() async {
    exchangeFormKeyAutovalidateMode.value = AutovalidateMode.always;
    if (exchangeFormKey.currentState?.validate() != true) return;
    Get.toNamed(PortabilityRequestAssetSelectionPage.routeName);
  }

  // Seleção de ativos
  final assets = <PortabilityAsset>[].obs;
  final assetFormKey = GlobalKey<FormState>();
  final assetFormKeyAutovalidateMode = AutovalidateMode.disabled.obs;

  // Controle de seções expandidas
  final expandedSection = Rxn<String>();

  // Controladores de texto para inputs de quantidade
  final Map<int, TextEditingController> quantityControllers = {};
  final Map<int, FocusNode> quantityFocusNodes = {};

  // Timer para debounce das atualizações de quantidade
  Timer? _updateTimer;

  Future<void> fetchAsstes() async {
    // TODO: Replace with actual API call
    // final assetList = await _transfersApi.getAssetList();

    // Mock data for testing
    final assetList = [
      PortabilityAsset(id: 1, type: 'Renda Fixa', name: 'CDB Banco Master S/A', availableQuantity: 121200),
      PortabilityAsset(id: 2, type: 'Renda Fixa', name: 'CDB Will Financeira S/A Crédito Privado', availableQuantity: 18400),
      PortabilityAsset(id: 3, type: 'Renda Fixa', name: 'CDB Banco Master S/A', availableQuantity: 38),
      PortabilityAsset(id: 4, type: 'Renda Fixa', name: 'CDB Will Financeira S/A Crédito Privado', availableQuantity: 90),
      PortabilityAsset(id: 5, type: 'Renda Fixa', name: 'CDB Banco Master S/A', availableQuantity: 6),
      PortabilityAsset(id: 6, type: 'Renda Variável', name: 'PETR4', availableQuantity: 100),
      PortabilityAsset(id: 7, type: 'Renda Variável', name: 'VALE3', availableQuantity: 200),
      PortabilityAsset(id: 8, type: 'Proventos', name: 'Dividendos PETR4', availableQuantity: 50),
      PortabilityAsset(id: 9, type: 'Fundos', name: 'Fundo XP Allocation', availableQuantity: 1000),
    ];

    assets.assignAll(assetList);

    // Inicializar controladores para cada ativo
    for (final asset in assetList) {
      if (asset.id != null) {
        quantityControllers[asset.id!] = TextEditingController(
          text: asset.selectedQuantity?.toString() ?? '',
        );
        quantityFocusNodes[asset.id!] = FocusNode();
      }
    }
  }

  // Agrupar ativos por tipo
  Map<String, List<PortabilityAsset>> get assetsByType {
    final Map<String, List<PortabilityAsset>> grouped = {};
    for (final asset in assets) {
      final type = asset.type ?? 'Outros';
      grouped[type] = (grouped[type] ?? [])..add(asset);
    }
    return grouped;
  }

  // Alternar seção expandida
  void toggleSection(String sectionName) {
    if (expandedSection.value == sectionName) {
      expandedSection.value = null;
    } else {
      expandedSection.value = sectionName;
    }
  }

  // Selecionar/deselecionar ativo
  void toggleAssetSelection(PortabilityAsset asset, bool isSelected) {
    asset.isSelected = isSelected;

    if (isSelected && asset.id != null) {
      // Focar no input quando selecionado
      quantityFocusNodes[asset.id!]?.requestFocus();
    } else {
      // Limpar quantidade quando desmarcado
      asset.selectedQuantity = null;
      if (asset.id != null) {
        quantityControllers[asset.id!]?.clear();
      }
    }

    assets.refresh();
  }

  // Selecionar todos os ativos de uma categoria
  void toggleSelectAllInCategory(String category, bool selectAll) {
    final categoryAssets = assetsByType[category] ?? [];
    for (final asset in categoryAssets) {
      toggleAssetSelection(asset, selectAll);
    }
  }

  // Definir quantidade máxima para um ativo
  void setMaxQuantity(PortabilityAsset asset) {
    if (asset.id != null && asset.availableQuantity != null) {
      asset.selectedQuantity = asset.availableQuantity;
      quantityControllers[asset.id!]?.text = asset.availableQuantity.toString();
      assets.refresh();
    }
  }

  // Atualizar quantidade selecionada
  void updateSelectedQuantity(PortabilityAsset asset, String value) {
    final quantity = double.tryParse(value);
    asset.selectedQuantity = quantity;

    // Use debounced update to avoid setState during build
    _updateTimer?.cancel();
    _updateTimer = Timer(const Duration(milliseconds: 100), () {
      assets.refresh();
    });
  }

  // Verificar se todos os ativos de uma categoria estão selecionados
  bool areAllSelectedInCategory(String category) {
    final categoryAssets = assetsByType[category] ?? [];
    return categoryAssets.isNotEmpty && categoryAssets.every((asset) => asset.isSelected);
  }

  // Contar ativos selecionados em uma categoria
  int getSelectedCountInCategory(String category) {
    final categoryAssets = assetsByType[category] ?? [];
    return categoryAssets.where((asset) => asset.isSelected).length;
  }

  @override
  void onClose() {
    // Limpar timer
    _updateTimer?.cancel();

    // Limpar controladores
    for (final controller in quantityControllers.values) {
      controller.dispose();
    }
    for (final focusNode in quantityFocusNodes.values) {
      focusNode.dispose();
    }
    super.onClose();
  }
}
