import 'package:get/get.dart';

import '../../api/transfers/transfers_api.dart';
import '../../models/custody_transfer_request.dart';

class PortabilityHistoryController extends GetxController {
  final _transfersApi = Get.find<TransfersApi>();

  final requests = <CustodyTransferRequest>[].obs;
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchPortabilityRequests();
  }

  static Future<void> deleteInstance() async {
    if (Get.isRegistered<PortabilityHistoryController>()) await Get.delete<PortabilityHistoryController>(force: true);
  }

  /// Busca a lista de solicitações de portabilidade.
  Future<void> fetchPortabilityRequests() async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      final portabilityRequests = await _transfersApi.getPortabilityRequests();
      requests.assignAll(portabilityRequests);
    } catch (error) {
      // Trata o erro silenciosamente para evitar interromper a UI
      requests.clear();
    } finally {
      isLoading.value = false;
    }
  }

  /// Retorna a contagem de solicitações de portabilidade pendentes.
  int get pendingRequestsCount {
    return requests.where((request) => request.status == CustodyTransferRequestStatus.pending).length;
  }
}
