import 'package:get/get.dart';

import '../../api/strategy_trade/strategy_trade_api.dart';
import '../../models/trade/trade.dart';
import '../../pages/trade/strategy_description_page.dart';
import '../../pages/trade/strategy_hiring_page.dart';
import '../../pages/trade/strategy_trade_how_it_works_page.dart';
import '../../utils/extensions.dart';
import 'trade_list_page_controller.dart';
import 'strategy_history_page_controller.dart';

class StrategyListPageController extends TradeListPageController {
  final _strategyTradeApi = Get.find<StrategyTradeApi>();

  @override
  String get pageTitle => 'Trade Quant';

  @override
  String get availableTradesTabText => 'Estratégias\ndisponíveis';

  @override
  String get hiredTradesTabText => 'Estratégias\ncontratadas';

  @override
  void showInfoPage() {
    Get.toNamed(StrategyTradeHowItWorksPage.routeName);
  }

  @override
  void showProfilePage(Trade trade) => Get.toNamed(StrategyDescriptionPage.routeName, arguments: trade);

  @override
  void showHistoryPage(Trade trade) => StrategyHistoryPageController.tryNavigateToStrategyHistory(trade);

  @override
  void showHiringPage(Trade trade) => Get.toNamedAndPopExistent(StrategyHiringPage.routeName.withId(trade.id), arguments: trade);

  @override
  String cardTitle(Trade trade) => trade.firstName ?? '';

  @override
  Future<(List<Trade>, List<Trade>)> getTrades() async {
    final response = await _strategyTradeApi.getStrategies();
    return (
      response.availableStrategies.sortedByPerformance,
      response.hiredStrategies,
    );
  }

  @override
  String get deactivatedText => 'A Estratégia não está mais disponível';
}
