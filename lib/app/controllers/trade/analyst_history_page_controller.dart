import 'package:get/get.dart';

import '../../api/analyst_trade/analyst_trade_api.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_open_position.dart';
import '../../pages/trade/analyst_history_page.dart';
import '../../utils/extensions.dart';
import 'trade_history_page_controller.dart';

class AnalystHistoryPageController extends TradeHistoryPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get tradeNotFoundText => 'Analista não encontrado';

  @override
  Future<Trade> getTradeById(String id) => _analystTradeApi.getAnalystById(id);

  @override
  Future<TradeHistory> getTradeHistory({required int id, required DateTime monthDate}) {
    return _analystTradeApi.getAnalystHistory(analystId: id, monthDate: monthDate);
  }

  @override
  Future<TradeOpenPositionsResponse> getTradeOpenPositions({required int id}) {
    return _analystTradeApi.getAnalystOpenPositions(analystId: id);
  }

  @override
  Future<TradeClosedPositionsResponse> getTradeClosedPositions({required int id, required DateTime monthDate}) {
    return _analystTradeApi.getAnalystClosedPositions(analystId: id, monthDate: monthDate);
  }

  static void tryNavigateToAnalystHistory(Trade analyst) {
    if (TradeHistoryPageController.checkHistory(analyst)) {
      Get.toNamedAndPopExistent(
        AnalystHistoryPage.routeName.withId(analyst.id),
        arguments: analyst,
      );
    }
  }
}
