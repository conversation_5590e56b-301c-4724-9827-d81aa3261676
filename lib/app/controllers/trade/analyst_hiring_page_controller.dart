import 'package:get/get.dart';

import '../../api/analyst_trade/analyst_trade_api.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../../pages/trade/analyst_list_page.dart';
import '../../pages/trade/analyst_my_list_page.dart';
import 'analyst_list_page_controller.dart';
import 'trade_hiring_page_controller.dart';
import 'trade_list_page_controller.dart';

class AnalystHiringPageController extends TradeHiringPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive}) {
    return _analystTradeApi.getAnalystRequiredBalanceForAmount(analystId: id, amount: amount, isActive: isActive);
  }

  @override
  String get serviceTitle => 'Trade do Analista';

  @override
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _analystTradeApi.editAnalyst(
      analystId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _analystTradeApi.hireAnalyst(
      analystId: id,
      amount: amount,
      signature: signature,
      didAcceptTerms: didAcceptTerms,
      isProfileSuitabilityUnfit: isProfileSuitabilityUnfit,
      contractCount: contractCount,
    );
  }

  @override
  String get investSuccessMessage =>
      'Operações recomendadas pelo Analista a partir de amanhã serão efetuadas automaticamente com o novo valor até a desativação do serviço ou nova edição.';

  @override
  String get deactivationConfirmationMessage => 'Ao desativar este serviço, operações recomendadas pelo Analista não serão mais efetuadas automaticamente.';

  @override
  Future<void> deactivateTrade({required int id, required List<List<int>> signature}) {
    return _analystTradeApi.deactivateAnalyst(analystId: id, signature: signature);
  }

  @override
  String get purchaseListPageRouteName => AnalystListPage.routeName;

  @override
  String get myInvestmentsListPageRouteName => AnalystMyListPage.routeName;

  @override
  bool get isListPageControllerRegistered => Get.isRegistered<AnalystListPageController>();

  @override
  TradeListPageController get listPageController => Get.find<AnalystListPageController>();

  @override
  Future<Trade> getTradeById(String id) => _analystTradeApi.getAnalystById(id);

  @override
  String get tradeTile => 'Analista';

  @override
  String tradeName(Trade? trade) => trade?.fullName ?? '';

  @override
  int get tradeTermsId => 33;

  @override
  int get distributorFeeTextId => 1;
}
