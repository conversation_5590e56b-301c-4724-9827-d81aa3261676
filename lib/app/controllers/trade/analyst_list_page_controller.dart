import 'package:get/get.dart';

import '../../api/analyst_trade/analyst_trade_api.dart';
import '../../models/trade/trade.dart';
import '../../pages/trade/analyst_description_page.dart';
import '../../pages/trade/analyst_hiring_page.dart';
import '../../pages/trade/analyst_trade_how_it_works_page.dart';
import '../../utils/extensions.dart';
import 'trade_list_page_controller.dart';
import 'analyst_history_page_controller.dart';

class AnalystListPageController extends TradeListPageController {
  final _analystTradeApi = Get.find<AnalystTradeApi>();

  @override
  String get pageTitle => 'Trade do Analista';

  @override
  String get availableTradesTabText => 'Analistas disponíveis';

  @override
  String get hiredTradesTabText => 'Analistas contratados';

  @override
  void showInfoPage() {
    Get.toNamed(AnalystTradeHowItWorksPage.routeName);
  }

  @override
  void showProfilePage(Trade trade) => Get.toNamed(AnalystDescriptionPage.routeName, arguments: trade);

  @override
  void showHistoryPage(Trade trade) => AnalystHistoryPageController.tryNavigateToAnalystHistory(trade);

  @override
  void showHiringPage(Trade trade) => Get.toNamedAndPopExistent(AnalystHiringPage.routeName.withId(trade.id), arguments: trade);

  @override
  String cardTitle(Trade trade) => trade.fullName;

  @override
  Future<(List<Trade>, List<Trade>)> getTrades() async {
    final response = await _analystTradeApi.getAnalysts();
    return (
      response.availableAnalysts.sortedByPerformance.alteranatedByMarket,
      response.hiredAnalysts.sortedByPerformance.alteranatedByMarket,
    );
  }

  @override
  bool get isShowMoreButtonVisible => false;

  @override
  List<Trade> get relevantAvailableTrades => availableTrades;

  @override
  String get deactivatedText => 'O Analista não está mais disponível';
}
