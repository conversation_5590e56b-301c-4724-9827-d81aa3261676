import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../errors/error_handlers.dart';
import '../../models/trade/trade.dart';
import '../../utils/extensions.dart';

abstract class TradeListPageController extends GetxController with GetSingleTickerProviderStateMixin {
  final availableTrades = <Trade>[];
  final hiredTrades = <Trade>[];
  late final tabController = TabController(length: 2, vsync: this);
  final minimumToShow = 3;

  bool isLoading = true;
  bool _isShowingMore = false;
  bool get isShowingMore => _isShowingMore;
  set isShowingMore(bool value) {
    _isShowingMore = value;
    update();
  }

  // `onReady` utilizado ao invés de `onInit` para evitar erro ao mostrar modal de questionário suitability.
  @override
  void onReady() {
    super.onReady();
    loadTrades();
    tabController.navigateToTabQueryParam();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  String get pageTitle;
  String get availableTradesTabText;
  String get hiredTradesTabText;
  void showInfoPage();
  void showProfilePage(Trade trade);
  void showHistoryPage(Trade trade);
  void showHiringPage(Trade trade);
  String cardTitle(Trade trade);
  Future<(List<Trade>, List<Trade>)> getTrades();
  String get deactivatedText;

  Future<void> loadTrades() async {
    isLoading = true;
    update();
    try {
      final (available, hired) = await getTrades();
      availableTrades.assignAll(available);
      for (var trade in hired) {
        trade.isHired = true;
      }
      hiredTrades.assignAll(hired);
    } catch (error) {
      onError(error);
    } finally {
      isLoading = false;
      update();
    }
  }

  bool get isShowMoreButtonVisible {
    return availableTrades.negativePerformance.isNotEmpty && !isShowingMore && availableTrades.length > minimumToShow;
  }

  List<Trade> get relevantAvailableTrades {
    if (isShowingMore) return availableTrades;
    final sliceOn = availableTrades.length < minimumToShow ? availableTrades.length : minimumToShow;
    return availableTrades.positivePerformance.length < minimumToShow ? availableTrades.sublist(0, sliceOn) : availableTrades.positivePerformance;
  }
}
