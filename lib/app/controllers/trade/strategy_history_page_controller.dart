import 'package:get/get.dart';

import '../../api/strategy_trade/strategy_trade_api.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_open_position.dart';
import '../../pages/trade/strategy_history_page.dart';
import '../../utils/extensions.dart';
import 'trade_history_page_controller.dart';

class StrategyHistoryPageController extends TradeHistoryPageController {
  final _strategyTradeApi = Get.find<StrategyTradeApi>();

  @override
  String get tradeNotFoundText => 'Estratégia não encontrada';

  @override
  Future<Trade> getTradeById(String id) => _strategyTradeApi.getStrategyById(id);

  @override
  Future<TradeHistory> getTradeHistory({required int id, required DateTime monthDate}) {
    return _strategyTradeApi.getStrategyHistory(strategyId: id, monthDate: monthDate);
  }

  @override
  Future<TradeOpenPositionsResponse> getTradeOpenPositions({required int id}) {
    return _strategyTradeApi.getStrategyOpenPositions(strategyId: id);
  }

  @override
  Future<TradeClosedPositionsResponse> getTradeClosedPositions({required int id, required DateTime monthDate}) {
    return _strategyTradeApi.getStrategyClosedPositions(strategyId: id, monthDate: monthDate);
  }

  static void tryNavigateToStrategyHistory(Trade strategy) {
    if (TradeHistoryPageController.checkHistory(strategy)) {
      Get.toNamedAndPopExistent(
        StrategyHistoryPage.routeName.withId(strategy.id),
        arguments: strategy,
      );
    }
  }
}
