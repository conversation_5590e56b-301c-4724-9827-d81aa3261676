import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../models/trade/trade_closed_position.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_open_position.dart';
import '../../utils/extensions.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';

abstract class TradeHistoryPageController extends GetxController {
  Trade? trade;
  TradeHistory? tradeHistory;
  TradeOpenPositionsResponse? openPositions;
  TradeClosedPositionsResponse? closedPositions;
  final selectedMonth = Rx<DateTime?>(null);

  final isLoadingStrategy = false.obs;
  final isLoadingHistory = false.obs;
  final didThrowHistoryError = false.obs;
  final isLoadingOpenPositions = false.obs;
  final isLoadingClosedPositions = false.obs;
  final didThrowClosedPositionsError = false.obs;

  bool get hasOpenPosition => (openPositions?.positions ?? []).isNotEmpty && !isLoadingOpenPositions.value;

  @override
  void onInit() {
    super.onInit();
    loadTradeAndData();
  }

  String get tradeNotFoundText;
  Future<Trade> getTradeById(String id);
  Future<TradeHistory> getTradeHistory({required int id, required DateTime monthDate});
  Future<TradeOpenPositionsResponse> getTradeOpenPositions({required int id});
  Future<TradeClosedPositionsResponse> getTradeClosedPositions({required int id, required DateTime monthDate});

  Future<bool> loadTradeAndData() async {
    await getTrade();
    if (trade == null) throw Exception(tradeNotFoundText);

    trade?.monthsWithActivity.sort((a, b) => b.compareTo(a));
    selectedMonth.value = trade?.monthsWithActivity.firstOrNull;

    if (trade!.isHired) {
      await Future.wait([
        getOpenPositions(),
        getClosedPositions(selectedMonth.value, shouldThrowError: true),
      ]);
    } else {
      await getHistory(selectedMonth.value, shouldThrowError: true);
    }

    return true;
  }

  Future<void> getTrade() async {
    isLoadingStrategy.value = true;

    try {
      if (Get.arguments is Trade) {
        trade = Get.arguments as Trade;
      } else {
        trade = await getTradeById(Get.parameters['id'] ?? '');
      }
    } finally {
      isLoadingStrategy.value = false;
    }
  }

  Future<void> getHistory(DateTime? monthDate, {shouldThrowError = false}) async {
    if (monthDate == null) return;

    isLoadingHistory.value = true;

    try {
      tradeHistory = await getTradeHistory(id: trade!.id ?? 0, monthDate: monthDate);
      didThrowHistoryError.value = false;
    } catch (_) {
      didThrowHistoryError.value = true;
      if (shouldThrowError) rethrow;
    } finally {
      isLoadingHistory.value = false;
    }
  }

  Future<void> getOpenPositions() async {
    isLoadingOpenPositions.value = true;
    try {
      openPositions = await getTradeOpenPositions(id: trade!.id ?? 0);
    } finally {
      isLoadingOpenPositions.value = false;
    }
  }

  Future<void> getClosedPositions(DateTime? monthDate, {shouldThrowError = false}) async {
    if (monthDate == null) return;

    isLoadingClosedPositions.value = true;
    try {
      closedPositions = await getTradeClosedPositions(id: trade!.id ?? 0, monthDate: monthDate);
      didThrowClosedPositionsError.value = false;
    } catch (_) {
      didThrowClosedPositionsError.value = true;
      if (shouldThrowError) rethrow;
    } finally {
      isLoadingClosedPositions.value = false;
    }
  }

  List<DateTime> filterMonths(String search) {
    return (trade?.monthsWithActivity ?? []).where((date) => date.toMonthAndYear().matchToSearch(search)).toList();
  }

  static bool checkHistory(Trade trade) {
    if (!trade.isHired && !trade.hasHistory) {
      CMBottomSheet.simple(
        topWidget: Image.asset('assets/icons/dialog/scheduling_confirmed.png'),
        title: 'Ainda não há histórico',
        description: 'Em breve as recomendações encerradas da Estratégia aparecerão aqui. Por enquanto você pode conferir as recomendações em aberto na área Recomendações.',
      ).show();
      return false;
    }
    return true;
  }
}
