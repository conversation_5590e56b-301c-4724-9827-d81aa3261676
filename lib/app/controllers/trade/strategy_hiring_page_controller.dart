import 'package:get/get.dart';

import '../../models/trade/trade.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../../pages/tabs/my_investments/my_investments.dart';
import '../../api/strategy_trade/strategy_trade_api.dart';
import '../../pages/trade/strategy_list_page.dart';
import 'strategy_list_page_controller.dart';
import 'trade_hiring_page_controller.dart';
import 'trade_list_page_controller.dart';

class StrategyHiringPageController extends TradeHiringPageController {
  final _strategyTradeApi = Get.find<StrategyTradeApi>();

  @override
  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive}) {
    return _strategyTradeApi.getStrategyRequiredBalanceForAmount(strategyId: id, amount: amount, isActive: isActive);
  }

  @override
  String get serviceTitle => 'Trade Quant';

  @override
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _strategyTradeApi.editStrategy(
        strategyId: id, amount: amount, signature: signature, didAcceptTerms: didAcceptTerms, isProfileSuitabilityUnfit: isProfileSuitabilityUnfit);
  }

  @override
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  }) {
    return _strategyTradeApi.hireStrategy(
        strategyId: id, amount: amount, signature: signature, didAcceptTerms: didAcceptTerms, isProfileSuitabilityUnfit: isProfileSuitabilityUnfit);
  }

  @override
  String get investSuccessMessage =>
      'Operações recomendadas pela Estratégia a partir de amanhã serão efetuadas automaticamente com o novo valor até a desativação do serviço ou nova edição.';

  @override
  String get deactivationConfirmationMessage => 'Ao desativar este serviço, operações recomendadas pela Estratégia não serão mais efetuadas automaticamente.';

  @override
  Future<void> deactivateTrade({required int id, required List<List<int>> signature}) {
    return _strategyTradeApi.deactivateStrategy(strategyId: id, signature: signature);
  }

  @override
  String get purchaseListPageRouteName => StrategyListPage.routeName;

  @override
  String get myInvestmentsListPageRouteName => StrategyMyListPage.routeName;

  @override
  bool get isListPageControllerRegistered => Get.isRegistered<StrategyListPageController>();

  @override
  TradeListPageController get listPageController => Get.find<StrategyListPageController>();

  @override
  Future<Trade> getTradeById(String id) => _strategyTradeApi.getStrategyById(id);

  @override
  String get tradeTile => 'Estratégia';

  @override
  String tradeName(Trade? trade) => trade?.firstName ?? '';

  @override
  int get tradeTermsId => 34;

  @override
  int get distributorFeeTextId => 2;
}
