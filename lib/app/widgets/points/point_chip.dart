import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';

class PointChip extends StatelessWidget {
  final double points;
  final Color color;
  final Color negativeColor;
  final Color positiveColor;
  final String? preffix;
  final String? suffix;
  final double fontSize;

  String get _text {
     var text = (preffix ?? '').isNotEmpty ? '$preffix ' : '';
    text += points.toPointsString();
    text += (suffix ?? '').isNotEmpty ? ' $suffix' : '';
    return text;
  }

  const PointChip(
    this.points, {
    super.key,
    this.negativeColor = const Color(0xFF9A0000),
    this.positiveColor = const Color(0xFF00701F),
    this.fontSize = 14,
    this.preffix,
    this.suffix,
  }) : color = points >= 0 ? positiveColor : negativeColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        _text,
        style: AppTheme.bold14White.copyWith(color: const Color(0xFFF5F5F5), fontSize: fontSize),
      ),
    );
  }
}
