import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../config/app_theme.dart';

class QuantityInput extends StatefulWidget {
  const QuantityInput({
    super.key,
    this.controller,
    this.focusNode,
    this.enabled = true,
    this.hintText = '0',
    this.onChanged,
    this.onMaxPressed,
    this.validator,
    this.maxValue,
    this.showMaxButton = true,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool enabled;
  final String hintText;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onMaxPressed;
  final FormFieldValidator<String>? validator;
  final double? maxValue;
  final bool showMaxButton;

  @override
  State<QuantityInput> createState() => _QuantityInputState();
}

class _QuantityInputState extends State<QuantityInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _hasFocus = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.controller == null) _controller.dispose();
    if (widget.focusNode == null) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });
  }

  void _onChanged(String value) {
    // Validate the input
    if (widget.validator != null) {
      setState(() {
        _errorText = widget.validator!(value);
      });
    }

    widget.onChanged?.call(value);
  }

  void _onMaxPressed() {
    if (widget.maxValue != null) {
      _controller.text = widget.maxValue!.toInt().toString();
      _onChanged(_controller.text);
    }
    widget.onMaxPressed?.call();
  }

  Color get _borderColor {
    if (!widget.enabled) return AppTheme.greyColor4;
    if (_errorText != null) return AppTheme.errorColor;
    if (_hasFocus) return AppTheme.orangeColor;
    return AppTheme.greyColor3;
  }

  Color get _textColor {
    if (!widget.enabled) return AppTheme.greyColor4;
    return AppTheme.whiteColor;
  }

  Color get _maxButtonColor {
    if (!widget.enabled) return AppTheme.greyColor4;
    return AppTheme.orangeColor;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 32,
          decoration: BoxDecoration(
            border: Border.all(
              color: _borderColor,
              width: _hasFocus ? 2.0 : 1.0,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              // Input field
              Expanded(
                child: TextFormField(
                  controller: _controller,
                  focusNode: _focusNode,
                  enabled: widget.enabled,
                  textAlign: TextAlign.right,
                  style: AppTheme.regular12White.copyWith(
                    color: _textColor,
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 6,
                    ),
                    hintText: widget.hintText,
                    hintStyle: AppTheme.regular12White.copyWith(
                      color: widget.enabled ? AppTheme.greyColor3.withValues(alpha: 0.6) : AppTheme.greyColor4,
                    ),
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChanged: _onChanged,
                ),
              ),

              // Max button
              if (widget.showMaxButton) ...[
                Container(
                  width: 1,
                  height: 20,
                  color: _borderColor,
                ),
                GestureDetector(
                  onTap: widget.enabled ? _onMaxPressed : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      'Max',
                      style: AppTheme.regular12White.copyWith(
                        color: _maxButtonColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),

        // Error text
        if (_errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            _errorText!,
            style: AppTheme.regular10White.copyWith(
              color: AppTheme.errorColor,
            ),
          ),
        ],
      ],
    );
  }
}
