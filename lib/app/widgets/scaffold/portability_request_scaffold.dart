import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../buttons/button.dart';
import 'app_scaffold.dart';

class PortabilityRequestScaffold extends StatelessWidget {
  const PortabilityRequestScaffold({
    super.key,
    required this.title,
    required this.subtitle,
    this.bottomButtonText,
    this.bottomButtonIcon,
    this.bottomButtonOnPressed,
    this.topText,
    this.onGetData,
    this.formKey,
    this.formAutovalidateMode,
    this.columnChildren = const [],
  });

  final String title;
  final String subtitle;

  /// Quando informado, botão fixo é mostrado no rodapé.
  final String? bottomButtonText;
  final dynamic bottomButtonIcon;

  /// Quando nulo, botão é desabilitado.
  final VoidCallback? bottomButtonOnPressed;
  final String? topText;
  final Future<dynamic> Function()? onGetData;
  final GlobalKey<FormState>? formKey;
  final AutovalidateMode? formAutovalidateMode;
  final List<Widget> columnChildren;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      // AppBar
      appBar: TopBar(title: title, subtitle: subtitle),

      // Botão fixo
      extendBody: bottomButtonText == null,
      bottomNavigationBar: bottomButtonText == null
          ? null
          : Container(
              padding: EdgeInsets.fromLTRB(24, 20, 24, max(24, Get.mediaQuery.viewPadding.bottom)),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(0xFF00375F), width: 1)),
                color: Color(0xFF0E1E2B),
              ),
              child: Button.elevated(
                text: bottomButtonText!,
                rightIcon: bottomButtonIcon,
                onPressed: bottomButtonOnPressed,
                disabled: bottomButtonOnPressed == null,
              ),
            ),

      // Conteúdo
      body: CMBody(
        onGetData: onGetData,
        ignoreEmptyState: true,
        child: SingleChildScrollView(
          padding: AppTheme.pagePadding,
          child: Form(
            key: formKey,
            autovalidateMode: formAutovalidateMode,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if ((topText ?? '').isNotEmpty) Text(topText!, style: AppTheme.bold16White),
                ...columnChildren,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
