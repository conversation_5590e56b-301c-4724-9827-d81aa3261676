import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';

class PortabilityChoiceIcon extends StatelessWidget {
  const PortabilityChoiceIcon(
    this.iconPath, {
    super.key,
    this.padding = const EdgeInsets.all(5),
  });

  final String iconPath;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      height: 48,
      padding: padding,
      child: SvgPicture.asset(
        iconPath,
        colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
      ),
    );
  }
}

class PortabilityChoiceCmCapitalIcon extends PortabilityChoiceIcon {
  const PortabilityChoiceCmCapitalIcon({
    super.key,
    super.padding = const EdgeInsets.all(5),
  }) : super('assets/images/cm-capital-icon-transparent.svg');
}

class PortabilityChoiceB3Icon extends PortabilityChoiceIcon {
  const PortabilityChoiceB3Icon({
    super.key,
    super.padding = const EdgeInsets.fromLTRB(12, 8, 4, 8),
  }) : super('assets/icons/b3.svg');
}
