import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';

class PortabilityExchangeCard extends StatelessWidget {
  const PortabilityExchangeCard({
    super.key,
    required this.name,
    this.isCmCapital = false,
    this.isOrigin = false,
    this.direction = Axis.horizontal,
  });

  final String name;
  final bool isCmCapital;
  final bool isOrigin;
  final Axis direction;

  String get _sourceText => isOrigin ? 'Origem' : 'Destino';
  bool get _isHorizontal => direction == Axis.horizontal;
  String get _description {
    if (_isHorizontal) return 'Corretora de ${_sourceText.toLowerCase()} selecionada';
    return _sourceText;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isOrigin ? AppTheme.blueColor6 : null,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AppTheme.blueColor6, width: 1),
      ),
      child: Flex(
        direction: direction,
        children: [
          // Ícone
          isCmCapital
              ? const Icon(
                  Icons.paid_outlined,
                  size: 32,
                  color: AppTheme.whiteColor,
                )
              : SvgPicture.asset(
                  'assets/images/cm-capital-icon-transparent.svg',
                  colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                  height: 32,
                ),
          SizedBox(
            width: _isHorizontal ? 16 : null,
            height: _isHorizontal ? null : 16,
          ),

          // Name and description
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Descrição
                Text(_description, style: AppTheme.regular14Grey3),

                // Nome
                const SizedBox(height: 4),
                Text(name, style: AppTheme.bold16White),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
