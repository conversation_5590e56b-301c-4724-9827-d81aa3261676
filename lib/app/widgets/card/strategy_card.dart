import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/strategy_history_page_controller.dart';
import '../../models/trade/trade.dart';
import '../../pages/trade/strategy_description_page.dart';
import '../../pages/trade/strategy_hiring_page.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import '../info/cm_chip.dart';
import '../media/cached_image.dart';
import '../percentage/percentage_chip.dart';

class StrategyCard extends StatelessWidget {
  const StrategyCard(this.strategy, {super.key});

  final Trade strategy;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        color: AppTheme.whiteColor,
        child: Column(
          children: [
            Stack(
              children: [
                CachedImage(
                  imageUrl: strategy.mobileImageUrl ?? "",
                  width: double.infinity,
                  height: 186,
                  fit: BoxFit.cover,
                  errorWidget: Container(),
                ),
                Positioned(
                  top: 16,
                  right: 12,
                  child: Container(
                    decoration: const BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 4,
                          offset: Offset(0, 1),
                        ),
                      ],
                      shape: BoxShape.circle,
                    ),
                    child: Button.icon(
                      icon: Icons.info_outline,
                      onPressed: () => Get.toNamed(StrategyDescriptionPage.routeName, arguments: strategy),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Estratégia', style: AppTheme.regular12Black),
                      if (strategy.isHired)
                        const Text(
                          'Valor por operação',
                          style: AppTheme.regular12Black,
                        )
                      else if (strategy.hasHistory)
                        const Text(
                          'Resultado acumulado',
                          style: AppTheme.regular12Black,
                        ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: !strategy.hasHistory && !strategy.isHired ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(strategy.firstName ?? '', style: AppTheme.semi14Black),
                      ),
                      if (strategy.isHired)
                        Text(
                          strategy.isActive ? strategy.hiredValuePerOperation.asCurrency() : 'Desativado',
                          style: strategy.isActive ? AppTheme.semi14Black : AppTheme.semi14Red,
                        )
                      else if (!strategy.hasHistory)
                        const CMChip(
                          margin: EdgeInsets.only(left: 8),
                          color: AppTheme.orangeColor,
                          child: Text('NOVIDADE', style: AppTheme.bold14White),
                        )
                      else
                        PercentageChip(
                          strategy.performancePercentage ?? 0,
                        )
                    ],
                  ),
                  if (strategy.isHired)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      child: PercentageChip(strategy.performancePercentage ?? 0),
                    ),
                  if (strategy.isHired && strategy.isActive)
                    Button.elevated(
                      text: 'Editar',
                      onPressed: () {
                        Get.toNamedAndPopExistent(
                          StrategyHiringPage.routeName.withId(strategy.id),
                          arguments: strategy,
                        );
                      },
                    )
                  else
                    Button.elevated(
                      text: 'Ativar',
                      onPressed: () {
                        Get.toNamedAndPopExistent(
                          StrategyHiringPage.routeName.withId(strategy.id),
                          arguments: strategy,
                        );
                      },
                      margin: EdgeInsets.only(top: strategy.isHired ? 0 : 14),
                    ),
                  Button.outlined(
                    text: 'Histórico',
                    onPressed: () => StrategyHistoryPageController.tryNavigateToStrategyHistory(strategy),
                    textStyle: AppTheme.medium14Orange,
                    margin: const EdgeInsets.only(top: 8),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
