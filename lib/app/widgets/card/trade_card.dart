import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import '../info/app_tooltip.dart';
import '../info/icon_badge.dart';
import '../media/cached_image.dart';

class TradeCard extends StatelessWidget {
  final String profilePictureUrl;
  final String title;
  final String subtitle;
  final double? resultAmount;
  final double? successRate;
  final double? recommendationAverage;
  final double? operationAmount;
  final int? numberOfContracts;
  final VoidCallback? onProfilePressed;
  final VoidCallback? onHistoryPressed;
  final VoidCallback? onActivatePressed;
  final VoidCallback? onEditPressed;
  final bool isActive;
  final bool isHired;
  final bool hasHistory;
  final bool isBmf;
  final String deactivatedText;

  const TradeCard({
    super.key,
    required this.profilePictureUrl,
    required this.title,
    required this.subtitle,
    required this.resultAmount,
    required this.successRate,
    this.recommendationAverage,
    this.operationAmount,
    this.numberOfContracts,
    this.onProfilePressed,
    this.onHistoryPressed,
    this.onActivatePressed,
    this.onEditPressed,
    this.isActive = false,
    this.isHired = false,
    this.hasHistory = false,
    this.isBmf = false,
    required this.deactivatedText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: const Color(0xFF153043),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Perfil
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Avatar
              GestureDetector(
                onTap: onProfilePressed,
                child: ClipOval(
                  child: CachedImage(
                    imageUrl: profilePictureUrl,
                    placeholder: Container(),
                    errorWidget: Container(),
                    width: 45,
                    height: 45,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // Vídeo
              GestureDetector(
                onTap: onProfilePressed,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(width: 1, color: AppTheme.whiteColor),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.smart_display_outlined, size: 22),
                ),
              ),
            ],
          ),

          // Nome
          const SizedBox(height: 8),
          Text(title, style: AppTheme.bold16White),

          // Descrição
          const SizedBox(height: 4),
          Text(subtitle, style: AppTheme.regular12White),

          // Valores
          if (!isHired && !hasHistory)
            const IconBadge(
              margin: EdgeInsets.symmetric(vertical: 20),
              borderColor: Color(0xFFFF7F38),
              borderRadius: 4,
              backgroundColor: Color(0x20FF7F38),
              icon: Icons.verified_outlined,
              text: 'Novidade',
              textStyle: AppTheme.medium14Orange,
            )
          else
            Container(
              margin: const EdgeInsets.only(top: 8, bottom: 16),
              height: 42,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Resultado bruto
                  _ValueDisplay(
                    value: isBmf ? (resultAmount ?? 0).toPointsString() : (resultAmount ?? 0).asPercentage(showPlusSign: true),
                    valueColor: (resultAmount ?? 0) < 0 ? AppTheme.redColor4 : AppTheme.greenColor3,
                    description: isHired ? 'Seu resultado bruto' : 'Resultado bruto',
                    tooltipText: 'Resultado bruto dos últimos 12 meses'
                        '\nou desde o início das recomendações,'
                        '\nconsiderando o valor ${isHired ? 'aplicado' : 'mínimo aplicável'}.',
                  ),

                  const VerticalDivider(thickness: 1, width: 25, color: AppTheme.transparentWhiteColor),

                  // Taxa de acerto
                  _ValueDisplay(
                    value: successRate.asPercentage(),
                    description: 'Taxa de acerto',
                  ),

                  const VerticalDivider(thickness: 1, width: 25, color: AppTheme.transparentWhiteColor),

                  // Desativado
                  if (isHired && !isActive)
                    const _ValueDisplay(
                      value: 'Desativado',
                      valueColor: AppTheme.redColor4,
                      description: 'Valor por operação',
                    )
                  // Média por recomendação
                  else if (!isHired)
                    _ValueDisplay(
                      value: (recommendationAverage ?? 0).asPercentage(),
                      description: 'Média por recomend.',
                    )
                  // Valor por operação (BMF)
                  else if (isBmf)
                    _ValueDisplay(
                      value: (numberOfContracts ?? 0).formattedContract(),
                      description: (operationAmount ?? 0).asCurrency(),
                    )
                  // Valor por operação (Bovespa)
                  else
                    _ValueDisplay(
                      value: (operationAmount ?? 0).asCurrency(),
                      description: 'Valor por operação',
                    ),
                ],
              ),
            ),

          // Botões
          Row(
            children: [
              // Histórico
              Flexible(
                child: Opacity(
                  opacity: isHired || hasHistory ? 1 : 0.2,
                  child: Button.outlined(
                    height: 44,
                    margin: const EdgeInsets.only(right: 8),
                    text: 'Histórico',
                    borderColor: AppTheme.blueColor6,
                    leftIcon: 'assets/icons/calendar_clock.svg',
                    onPressed: onHistoryPressed,
                  ),
                ),
              ),

              // Desativado
              if (isHired && !isActive)
                Flexible(
                  child: Text(
                    deactivatedText.toUpperCase(),
                    style: AppTheme.bold10CardBg4,
                    textAlign: TextAlign.center,
                  ),
                )
              // Editar
              else if (isHired)
                Flexible(
                  child: Button.elevated(
                    height: 44,
                    text: 'Editar',
                    backgroundColor: AppTheme.blueColor6,
                    onPressed: onEditPressed,
                  ),
                )
              // Ativar
              else
                Flexible(
                  child: Button.elevated(
                    height: 44,
                    text: 'Ativar Agora',
                    onPressed: onActivatePressed,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ValueDisplay extends StatelessWidget {
  final String value;
  final Color valueColor;
  final String description;
  final String tooltipText;

  const _ValueDisplay({
    required this.value,
    this.valueColor = AppTheme.whiteColor,
    required this.description,
    this.tooltipText = '',
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: AppTooltip(
        message: tooltipText,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Valor
            Text(
              value,
              style: AppTheme.bold16White.copyWith(color: valueColor),
              overflow: TextOverflow.ellipsis,
            ),

            // Texto
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                description,
                style: AppTheme.regular10White.copyWith(
                  height: 1.8,
                  shadows: [const Shadow(color: Colors.white70, offset: Offset(0, -2))],
                  color: Colors.transparent,
                  decoration: TextDecoration.underline,
                  decorationStyle: TextDecorationStyle.dotted,
                  decorationColor: Colors.white70,
                  decorationThickness: 2,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
