import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/trade/analyst_history_page_controller.dart';
import '../../models/trade/trade.dart';
import '../../pages/trade/analyst_description_page.dart';
import '../../pages/trade/analyst_hiring_page.dart';
import '../../utils/extensions.dart';
import '../buttons/button.dart';
import '../info/cm_chip.dart';
import '../media/cached_image.dart';
import '../percentage/percentage_chip.dart';
import '../points/point_chip.dart';

class AnalystCard extends StatelessWidget {
  const AnalystCard(this.analyst, {super.key});

  final Trade analyst;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        color: AppTheme.whiteColor,
        child: Column(
          children: [
            Stack(
              children: [
                CachedImage(
                  imageUrl: analyst.profileImageUrl,
                  width: double.infinity,
                  height: 186,
                  fit: BoxFit.cover,
                  errorWidget: Container(),
                ),
                Positioned(
                  top: 16,
                  right: 12,
                  child: Container(
                    decoration: const BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 4,
                          offset: Offset(0, 1),
                        ),
                      ],
                      shape: BoxShape.circle,
                    ),
                    child: Button.icon(
                      icon: Icons.info_outline,
                      onPressed: () => Get.toNamed(AnalystDescriptionPage.routeName, arguments: analyst),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Analista', style: AppTheme.regular12Black),
                      if (analyst.isHired)
                        const Text(
                          'Valor por operação',
                          style: AppTheme.regular12Black,
                        )
                      else if (analyst.hasHistory)
                        const Text(
                          'Resultado acumulado',
                          style: AppTheme.regular12Black,
                        ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: !analyst.hasHistory && !analyst.isHired ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(analyst.fullName, style: AppTheme.semi14Black),
                      ),
                      if (analyst.isHired)
                        Text(
                          analyst.isActive
                              ? (analyst.isBmf
                                  ? '${analyst.hiredValuePerOperation.asCurrency()} (${analyst.numberOfContracts.formattedContract()})'
                                  : analyst.hiredValuePerOperation.asCurrency())
                              : 'Desativado',
                          style: analyst.isActive ? AppTheme.semi14Black : AppTheme.semi14Red,
                        )
                      else if (!analyst.hasHistory)
                        const CMChip(
                          margin: EdgeInsets.only(left: 8),
                          color: AppTheme.orangeColor,
                          child: Text('NOVIDADE', style: AppTheme.bold14White),
                        )
                      else
                        !analyst.isBmf ? PercentageChip(analyst.performancePercentage ?? 0) : PointChip(analyst.performancePercentage ?? 0)
                    ],
                  ),
                  if (analyst.isHired)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      child: !analyst.isBmf ? PercentageChip(analyst.performancePercentage ?? 0) : PointChip(analyst.performancePercentage ?? 0),
                    ),
                  if (analyst.isHired && analyst.isActive)
                    Button.elevated(
                      text: analyst.isHired && analyst.isActive ? 'Editar' : 'Ativar',
                      onPressed: () {
                        Get.toNamedAndPopExistent(
                          AnalystHiringPage.routeName.withId(analyst.id),
                          arguments: analyst,
                        );
                      },
                    )
                  else
                    Button.elevated(
                      text: 'Ativar',
                      onPressed: () {
                        Get.toNamedAndPopExistent(
                          AnalystHiringPage.routeName.withId(analyst.id),
                          arguments: analyst,
                        );
                      },
                      margin: EdgeInsets.only(top: analyst.isHired ? 0 : 14),
                    ),
                  Button.outlined(
                    text: 'Histórico',
                    onPressed: () => AnalystHistoryPageController.tryNavigateToAnalystHistory(analyst),
                    textStyle: AppTheme.medium14Orange,
                    margin: const EdgeInsets.only(top: 8),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
