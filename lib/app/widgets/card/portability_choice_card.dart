import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../info/cm_chip.dart';

class PortabilityChoiceCard extends StatelessWidget {
  const PortabilityChoiceCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    this.onTap,
    this.isRecommended = false,
  });

  final String title;
  final String description;
  final Widget icon;
  final VoidCallback? onTap;
  final bool isRecommended;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isRecommended ? AppTheme.blueColor6 : null,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: AppTheme.blueColor6, width: 1),
        ),
        child: Row(
          children: [
            // Ícone
            icon,
            const SizedBox(width: 16),

            // Title and description
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tag recomendado
                  if (isRecommended)
                    const CMChip(
                      margin: EdgeInsets.only(bottom: 4),
                      color: Color(0xFF084C54),
                      child: Text('RECOMENDADO', style: AppTheme.bold10Green3),
                    ),

                  // Título
                  Text(title, style: AppTheme.bold16White),

                  // Descrição
                  const SizedBox(height: 4),
                  Text(description, style: AppTheme.regular14Grey3),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
