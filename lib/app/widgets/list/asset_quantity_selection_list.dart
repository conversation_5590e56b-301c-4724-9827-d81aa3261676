import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/transfers/portability_to_request_controller.dart';
import '../../models/transfer/portability_asset.dart';
import '../../utils/ui_utils.dart';
import '../input/input.dart';
import '../input/labeled_checkbox.dart';

class AssetQuantitySelectionList extends StatelessWidget {
  const AssetQuantitySelectionList({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PortabilityToRequestController>();

    return Obx(() {
      final assetsByType = controller.assetsByType;

      if (assetsByType.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        children: assetsByType.entries.map((entry) {
          final categoryName = entry.key;
          final categoryAssets = entry.value;

          return _Section(
            name: categoryName,
            assets: categoryAssets,
            isExpanded: controller.expandedSection.value == categoryName,
            onToggleExpanded: () => controller.toggleSection(categoryName),
            onToggleSelectAll: (selectAll) => controller.toggleSelectAllInCategory(categoryName, selectAll),
            areAllSelected: controller.areAllSelectedInCategory(categoryName),
            selectedCount: controller.getSelectedCountInCategory(categoryName),
            controller: controller,
          );
        }).toList(),
      );
    });
  }
}

class _Section extends StatelessWidget {
  const _Section({
    super.key,
    required this.name,
    required this.assets,
    required this.isExpanded,
    required this.onToggleExpanded,
    required this.onToggleSelectAll,
    required this.areAllSelected,
    required this.selectedCount,
    required this.controller,
  });

  final String name;
  final List<PortabilityAsset> assets;
  final bool isExpanded;
  final VoidCallback onToggleExpanded;
  final Function(bool) onToggleSelectAll;
  final bool areAllSelected;
  final int selectedCount;
  final PortabilityToRequestController controller;

  String get _plural => selectedCount > 1 ? 's' : '';
  String get _totalSelectedAssetsText => '$selectedCount selecionado$_plural';

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // Cabeçalho
          GestureDetector(
            onTap: () {
              // Use post-frame callback to avoid setState during build
              WidgetsBinding.instance.addPostFrameCallback((_) {
                onToggleExpanded();
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                children: [
                  Expanded(child: Text(name, style: AppTheme.bold14White)),
                  Text(_totalSelectedAssetsText, style: AppTheme.regular12Grey2),
                  const SizedBox(width: 10),
                  iconify(
                    isExpanded ? Icons.keyboard_arrow_up_sharp : Icons.keyboard_arrow_down_sharp,
                    color: AppTheme.whiteColor,
                  ),
                ],
              ),
            ),
          ),

          // Conteúdo expandido
          if (isExpanded) ...[
            // Botão para seleção de todos
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  LabeledCheckbox(
                    value: areAllSelected,
                    onChanged: (value) {
                      // Use post-frame callback to avoid setState during build
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        onToggleSelectAll(value);
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Selecionar tudo de "$name"',
                    style: AppTheme.regular12White,
                  ),
                ],
              ),
            ),

            // Lista de ativos
            ...assets.map(
              (asset) => _Item(
                key: ValueKey(asset.id),
                asset: asset,
                controller: controller,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _Item extends StatelessWidget {
  const _Item({
    super.key,
    required this.asset,
    required this.controller,
  });

  final PortabilityAsset asset;
  final PortabilityToRequestController controller;

  @override
  Widget build(BuildContext context) {
    final isSelected = asset.isSelected;
    final quantityController = controller.quantityControllers[asset.id];
    final focusNode = controller.quantityFocusNodes[asset.id];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Checkbox
          LabeledCheckbox(
            value: isSelected,
            onChanged: (value) {
              // Use post-frame callback to avoid setState during build
              WidgetsBinding.instance.addPostFrameCallback((_) {
                controller.toggleAssetSelection(asset, value);
              });
            },
          ),

          // Ativo
          Expanded(
            child: Text(
              asset.name ?? '',
              style: AppTheme.regular12White,
            ),
          ),

          // Quantidade disponível
          const SizedBox(width: 8),
          Text(
            (asset.availableQuantity ?? 0).toString(),
            style: AppTheme.regular12Grey3,
          ),

          // Quantidade selecionada
          const SizedBox(width: 16),
          SizedBox(
            width: 100,
            child: Row(
              children: [
                Expanded(
                  child: Input(
                    controller: quantityController,
                    focusNode: focusNode,
                    enabled: isSelected,
                    autovalidate: AutovalidateMode.onUserInteraction,
                    padding: EdgeInsets.zero,
                    hintText: '0',
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      // Use post-frame callback to avoid setState during build
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        controller.updateSelectedQuantity(asset, value);
                      });
                    },
                    validator: (text) {
                      if (!isSelected) return null;
                      final value = double.tryParse(text ?? '') ?? 0;
                      if (value <= 0) return 'Inválido';
                      if (value > (asset.availableQuantity ?? 0)) return 'Máximo: ${asset.availableQuantity}';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: isSelected
                      ? () {
                          // Use post-frame callback to avoid setState during build
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            controller.setMaxQuantity(asset);
                          });
                        }
                      : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.orangeColor : AppTheme.greyColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Max',
                      style: AppTheme.regular12White.copyWith(
                        color: isSelected ? AppTheme.whiteColor : AppTheme.greyColor3,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
