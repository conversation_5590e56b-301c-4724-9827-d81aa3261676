import 'package:cm_capital_app/app/widgets/table/data_table.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/transfer/portability_asset.dart';
import '../../pages/tabs/patrimony/CM_sticky_headers_table.dart';
import '../../pages/tabs/patrimony/balance_sheet_data_set.dart';
import '../../utils/extensions.dart';
import '../../utils/ui_utils.dart';
import '../input/input.dart';
import '../input/labeled_checkbox.dart';

class AssetQuantitySelectionList extends StatelessWidget {
  const AssetQuantitySelectionList({super.key});

  @override
  Widget build(BuildContext context) {
    return _Section(
      name: 'Renda Fixa',
      assets: [
        PortabilityAsset(id: 1, name: 'CDB Banco Master S/A', availableQuantity: 15000, selectedQuantity: 10000),
        PortabilityAsset(id: 2, name: 'CDB Will Financeira S/A Crédito Privado', availableQuantity: 5000, selectedQuantity: 5000),
      ],
    );
  }
}

class _Section extends StatelessWidget {
  const _Section({
    super.key,
    required this.name,
    required this.assets,
    this.isExpanded = true,
  });

  final String name;
  final List<PortabilityAsset> assets;
  final bool isExpanded;

  String get _plural => assets.length > 1 ? 's' : '';
  String get _totalSelectedAssetsText => '${assets.length} ativo$_plural selecionado$_plural';

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Cabeçalho
        Row(
          children: [
            Expanded(child: Text(name, style: AppTheme.bold14White)),
            Text(_totalSelectedAssetsText, style: AppTheme.regular12Grey2),
            const SizedBox(width: 10),
            iconify(isExpanded ? Icons.keyboard_arrow_up_sharp : Icons.keyboard_arrow_down_sharp, color: AppTheme.whiteColor),
          ],
        ),

        // Botão para seleção de todos
        Container(),

        // Lista de ativos
        // Row(
        //   children: [
        //     // Check
        //     Column(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: List.generate(
        //         assets.length,
        //         (index) => Container(
        //           color: Colors.white24,
        //           height: 80,
        //           child: LabeledCheckbox(value: assets[index].isSelected, onChanged: (value) {}),
        //         ),
        //       ),
        //     ),

        //     // Ativo
        //     Expanded(
        //       child: Column(
        //         mainAxisAlignment: MainAxisAlignment.center,
        //         crossAxisAlignment: CrossAxisAlignment.start,
        //         children: List.generate(
        //           assets.length,
        //           (index) => Container(
        //             height: 80,
        //             child: Center(child: Text(assets[index].name ?? '', style: AppTheme.regular12White)),
        //           ),
        //         ),
        //       ),
        //     ),

        //     // Quantidade disponível
        //     Column(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: List.generate(
        //         assets.length,
        //         (index) => Container(
        //           height: 80,
        //           child: Center(child: Text(assets[index].availableQuantity.toString(), style: AppTheme.regular12White)),
        //         ),
        //       ),
        //     ),

        //     // Quantidade selecionada
        //     Column(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: List.generate(
        //         assets.length,
        //         (index) => Container(
        //           height: 80,
        //           width: 100,
        //           child: Center(
        //             child: Input(
        //               hintText: '0',
        //               keyboardType: TextInputType.number,
        //               controller: TextEditingController(text: assets[index].selectedQuantity.compact),
        //             ),
        //           ),
        //         ),
        //       ),
        //     ),
        //   ],
        // ),

        ...assets.map(
          (asset) => _Item(
            key: ValueKey(asset.id),
            name: asset.name ?? '',
            availableQuantity: asset.availableQuantity ?? 0,
            selectedQuantity: asset.selectedQuantity ?? 0,
          ),
        ),
      ],
    );
  }
}

class _Item extends StatelessWidget {
  _Item({
    super.key,
    this.isSelected = false,
    this.onSelected,
    required this.name,
    required this.availableQuantity,
    required this.selectedQuantity,
  });

  final bool isSelected;
  final void Function(bool isSelected)? onSelected;
  final String name;
  final double availableQuantity;
  final double selectedQuantity;

  void _onSelected(bool? value) {
    // if (value == true) _focusNode.requestFocus();
    onSelected?.call(value == true);
  }

  // final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Checkbox
        LabeledCheckbox(
          value: isSelected,
          onChanged: _onSelected,
        ),

        // Ativo
        Expanded(
          child: Text(name, style: AppTheme.regular12White),
        ),

        // Quantidade disponível
        const SizedBox(width: 8),
        Text(availableQuantity.toString(), style: AppTheme.regular12Grey3),

        // Quantidade selecionada
        const SizedBox(width: 16),
        Container(
          // decoration: BoxDecoration(border: Border.all(width: 1, color: Colors.amber)),
          width: 100,
          height: 86,
          child: Input(
            autovalidate: AutovalidateMode.onUserInteraction,
            padding: EdgeInsets.zero,
            // focusNode: _focusNode,
            hintText: '0',
            keyboardType: TextInputType.number,
            validator: (text) => (double.tryParse(text ?? '') ?? 0) < 1 ? 'Não pode ser zero' : null,
          ),
        ),
      ],
    );
  }
}
