import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../api/investments/credito-privado/credito_privado_api.dart';
import '../../api/investments/investment_product.dart';
import '../../config/app_theme.dart';
import '../../controllers/purchase/treasury_purchase_detail_controller.dart';
import '../../errors/error_handlers.dart';
import '../../pages/strategic_trade/strategic_list_page.dart';
import '../../pages/tabs/investments/investment_product_list_page.dart';
import '../../pages/tabs/investments/purchase/recommended_portfolio_purchase_list_page.dart';
import '../../pages/tabs/investments/recommendations_page.dart';
import '../../pages/trade/analyst_list_page.dart';
import '../../pages/trade/strategy_list_page.dart';
import '../../utils/pending_information.dart';
import '../home/<USER>';
import '../loading/loading.dart';
import '../modal/cm_bottom_sheet.dart';

class InvestmentCards extends StatelessWidget {
  final EdgeInsets padding;
  final Color iconColor;
  final Color cardsBackgroundColor;

  final isTesouroDiretoLoading = false.obs;
  final isPrivateCreditLoading = false.obs;

  InvestmentCards({
    super.key,
    this.padding = EdgeInsets.zero,
    Color? iconColor = AppTheme.blueColor8,
    this.cardsBackgroundColor = AppTheme.greyColor3,
  }) : iconColor = iconColor ?? AppTheme.whiteColor;

  Future<void> _goToTesouroDireto() async {
    if (isTesouroDiretoLoading.value == true) return;

    isTesouroDiretoLoading.value = true;

    try {
      await TreasuryPurchaseDetailController.checkIfUserCanOperateTreasury(
        ifUserCanOperate: () {
          Get.toNamed(InvestmentProductListPage.routeName, arguments: {'investmentType': InvestmentType.treasury});
        },
      );
    } catch (error) {
      onError(error);
    } finally {
      isTesouroDiretoLoading.value = false;
    }
  }

  Future<void> _goToPrivateCredit() async {
    if (isPrivateCreditLoading.value == true) return;

    isPrivateCreditLoading.value = true;

    try {
      final creditoPrivadoApi = Get.find<CreditoPrivadoApi>();
      final response = await creditoPrivadoApi.getDebenturesDisponiveis();
      switch (response.retornooperacao) {
        case 0:
          Get.toNamed(InvestmentProductListPage.routeName, arguments: {
            'investmentType': InvestmentType.privateCredit,
            'assets': response.produtos,
          });
          break;
        case 1:
        case 2:
        case 3:
          CMBottomSheet.simple(
            title: 'Mercado Fechado',
            topWidget: Column(
              children: [
                Image.asset('assets/icons/dialog/market_closed.png'),
                const SizedBox(height: 8),
              ],
            ),
            description: "${response.retornomensagem}\n\nTente novamente mais tarde.",
          ).show();
          break;
      }
    } catch (error) {
      onError(error);
    } finally {
      isPrivateCreditLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        children: [
          _Card(
            investmentType: InvestmentType.recommendedPortfolio,
            onShowInvestments: () => Get.toNamed(RecommendedPortfolioPurchaseListPage.routeName),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.analystTrade,
            onShowInvestments: () => Get.toNamed(AnalystListPage.routeName),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.strategicTrade,
            onShowInvestments: () => Get.toNamed(StrategicListPage.routeName),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.strategyTrade,
            onShowInvestments: () => Get.toNamed(StrategyListPage.routeName),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.recommendations,
            onShowInvestments: () => Get.toNamed(RecommendationsPage.routeName),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.investmentFunds,
            onShowInvestments: () => Get.toNamed(InvestmentProductListPage.routeName, arguments: {'investmentType': InvestmentType.investmentFunds}),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.fixedIncome,
            onShowInvestments: () => Get.toNamed(InvestmentProductListPage.routeName, arguments: {'investmentType': InvestmentType.fixedIncome}),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.treasury,
            onShowInvestments: _goToTesouroDireto,
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
            isLoading: isTesouroDiretoLoading,
          ),
          _Card(
            investmentType: InvestmentType.publicOffering,
            onShowInvestments: () => Get.toNamed(InvestmentProductListPage.routeName, arguments: {'investmentType': InvestmentType.publicOffering}),
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
          ),
          _Card(
            investmentType: InvestmentType.privateCredit,
            onShowInvestments: _goToPrivateCredit,
            iconColor: iconColor,
            backgroundColor: cardsBackgroundColor,
            isLoading: isPrivateCreditLoading,
          ),
        ],
      ),
    );
  }
}

class _Card extends StatelessWidget {
  final InvestmentType investmentType;
  final void Function() onShowInvestments;
  final Color iconColor;
  final Color backgroundColor;
  final RxBool? isLoading;

  _Card({
    required this.investmentType,
    required this.onShowInvestments,
    required this.iconColor,
    required this.backgroundColor,
    this.isLoading,
  });

  final _isCheckingPendingInformation = false.obs;
  bool get _isLoading => _isCheckingPendingInformation.isTrue || (isLoading?.isTrue ?? false);

  Future<void> _checkPendingInformationAndShowInvestments() async {
    _isCheckingPendingInformation.value = true;
    try {
      final hasPendingInformation = await checkPendingInformationForInvesting();
      if (!hasPendingInformation) onShowInvestments();
    } catch (error) {
      onError(error);
    } finally {
      _isCheckingPendingInformation.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => HomeCard(
        minHeight: 112,
        onTap: _isLoading ? null : _checkPendingInformationAndShowInvestments,
        icon: _isLoading
            ? const Loading()
            : SvgPicture.asset(
                investmentType.iconPath,
                colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
                height: 44,
              ),
        color: backgroundColor,
        description: Text(
          investmentType.text,
          style: AppTheme.semi15White.copyWith(color: iconColor, height: 1.2),
        ),
      ),
    );
  }
}
